{"ast": null, "code": "export const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};", "map": {"version": 3, "names": ["resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\functions-js\\src\\helper.ts"], "sourcesContent": ["import { Fetch } from './types'\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n"], "mappings": "AAEA,OAAO,MAAMA,YAAY,GAAIC,WAAmB,IAAW;EACzD,IAAIC,MAAa;EACjB,IAAID,WAAW,EAAE;IACfC,MAAM,GAAGD,WAAW;GACrB,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACvCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,OAAO,EAAEH;IAAK,CAAE,KAAKA,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;GACrF,MAAM;IACLF,MAAM,GAAGC,KAAK;;EAEhB,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}