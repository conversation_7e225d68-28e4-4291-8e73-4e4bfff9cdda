{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'http://localhost:54321';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'http://localhost:54321'\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key'\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface Profile {\n  id: string\n  email: string\n  full_name?: string\n  phone?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FoundDisc {\n  id: string\n  finder_id: string\n  brand: string\n  model?: string\n  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'\n  color: string\n  weight?: number\n  condition?: 'new' | 'excellent' | 'good' | 'fair' | 'poor'\n  plastic_type?: string\n  stamp_text?: string\n  phone_number?: string\n  name_on_disc?: string\n  location_found: string\n  location_coordinates?: { x: number; y: number }\n  found_date: string\n  description?: string\n  image_urls?: string[]\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  created_at: string\n  updated_at: string\n}\n\nexport interface LostDisc {\n  id: string\n  owner_id: string\n  brand: string\n  model?: string\n  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'\n  color: string\n  weight?: number\n  plastic_type?: string\n  stamp_text?: string\n  location_lost: string\n  location_coordinates?: { x: number; y: number }\n  lost_date: string\n  description?: string\n  reward_offered?: number\n  contact_preference: string\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  created_at: string\n  updated_at: string\n}\n\nexport interface DiscMatch {\n  id: string\n  found_disc_id: string\n  lost_disc_id: string\n  match_score: number\n  status: 'potential' | 'confirmed' | 'rejected'\n  finder_contacted_at?: string\n  owner_contacted_at?: string\n  created_at: string\n  updated_at: string\n  found_disc?: FoundDisc\n  lost_disc?: LostDisc\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AAEpD,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,wBAAwB;AAClF,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,eAAe;AAElF,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC;;AAElE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}