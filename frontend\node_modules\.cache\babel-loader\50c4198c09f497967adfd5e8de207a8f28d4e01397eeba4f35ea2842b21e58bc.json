{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    signOut\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleSignOut = async () => {\n    await signOut();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white shadow-lg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"text-2xl font-bold text-primary-600\",\n          children: \"DiscFinder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/report-found\",\n            className: \"text-gray-700 hover:text-primary-600 transition-colors\",\n            children: \"Report Found Disc\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/search-lost\",\n            className: \"text-gray-700 hover:text-primary-600 transition-colors\",\n            children: \"Search Lost Discs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), user && /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/my-reports\",\n            className: \"text-gray-700 hover:text-primary-600 transition-colors\",\n            children: \"My Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: user ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-700\",\n              children: [\"Welcome, \", user.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSignOut,\n              className: \"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors\",\n              children: \"Sign Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-primary-600 hover:text-primary-700 transition-colors\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden pb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/report-found\",\n            className: \"text-gray-700 hover:text-primary-600 transition-colors py-2\",\n            children: \"Report Found Disc\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/search-lost\",\n            className: \"text-gray-700 hover:text-primary-600 transition-colors py-2\",\n            children: \"Search Lost Discs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), user && /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/my-reports\",\n            className: \"text-gray-700 hover:text-primary-600 transition-colors py-2\",\n            children: \"My Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"atDpDTij6zr8IpXR1NZeWdTnnao=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "user", "signOut", "navigate", "handleSignOut", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/components/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Navbar: React.FC = () => {\n  const { user, signOut } = useAuth();\n  const navigate = useNavigate();\n\n  const handleSignOut = async () => {\n    await signOut();\n    navigate('/');\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-4\">\n          <Link to=\"/\" className=\"text-2xl font-bold text-primary-600\">\n            DiscFinder\n          </Link>\n          \n          <div className=\"hidden md:flex space-x-6\">\n            <Link \n              to=\"/report-found\" \n              className=\"text-gray-700 hover:text-primary-600 transition-colors\"\n            >\n              Report Found Disc\n            </Link>\n            <Link \n              to=\"/search-lost\" \n              className=\"text-gray-700 hover:text-primary-600 transition-colors\"\n            >\n              Search Lost Discs\n            </Link>\n            {user && (\n              <Link \n                to=\"/my-reports\" \n                className=\"text-gray-700 hover:text-primary-600 transition-colors\"\n              >\n                My Reports\n              </Link>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {user ? (\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-gray-700\">\n                  Welcome, {user.email}\n                </span>\n                <button\n                  onClick={handleSignOut}\n                  className=\"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors\"\n                >\n                  Sign Out\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link\n                  to=\"/login\"\n                  className=\"text-primary-600 hover:text-primary-700 transition-colors\"\n                >\n                  Sign In\n                </Link>\n                <Link\n                  to=\"/register\"\n                  className=\"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors\"\n                >\n                  Sign Up\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <div className=\"md:hidden pb-4\">\n          <div className=\"flex flex-col space-y-2\">\n            <Link \n              to=\"/report-found\" \n              className=\"text-gray-700 hover:text-primary-600 transition-colors py-2\"\n            >\n              Report Found Disc\n            </Link>\n            <Link \n              to=\"/search-lost\" \n              className=\"text-gray-700 hover:text-primary-600 transition-colors py-2\"\n            >\n              Search Lost Discs\n            </Link>\n            {user && (\n              <Link \n                to=\"/my-reports\" \n                className=\"text-gray-700 hover:text-primary-600 transition-colors py-2\"\n              >\n                My Reports\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGN,OAAO,CAAC,CAAC;EACnC,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMF,OAAO,CAAC,CAAC;IACfC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEL,OAAA;IAAKO,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjCR,OAAA;MAAKO,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCR,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDR,OAAA,CAACJ,IAAI;UAACa,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAE7D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEPb,OAAA;UAAKO,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCR,OAAA,CAACJ,IAAI;YACHa,EAAE,EAAC,eAAe;YAClBF,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACnE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPb,OAAA,CAACJ,IAAI;YACHa,EAAE,EAAC,cAAc;YACjBF,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACnE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNV,IAAI,iBACHH,OAAA,CAACJ,IAAI;YACHa,EAAE,EAAC,aAAa;YAChBF,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACnE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENb,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCL,IAAI,gBACHH,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,WACrB,EAACL,IAAI,CAACW,KAAK;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACPb,OAAA;cACEe,OAAO,EAAET,aAAc;cACvBC,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAClG;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENb,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CR,OAAA,CAACJ,IAAI;cACHa,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EACtE;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPb,OAAA,CAACJ,IAAI;cACHa,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAClG;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNb,OAAA;QAAKO,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BR,OAAA;UAAKO,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCR,OAAA,CAACJ,IAAI;YACHa,EAAE,EAAC,eAAe;YAClBF,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPb,OAAA,CAACJ,IAAI;YACHa,EAAE,EAAC,cAAc;YACjBF,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNV,IAAI,iBACHH,OAAA,CAACJ,IAAI;YACHa,EAAE,EAAC,aAAa;YAChBF,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CApGID,MAAgB;EAAA,QACMH,OAAO,EAChBD,WAAW;AAAA;AAAAmB,EAAA,GAFxBf,MAAgB;AAsGtB,eAAeA,MAAM;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}