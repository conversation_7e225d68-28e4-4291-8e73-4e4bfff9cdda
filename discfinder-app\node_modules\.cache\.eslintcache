[{"C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts": "4", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx": "5"}, {"size": 554, "mtime": 1751073000293, "results": "6", "hashOfConfig": "7"}, {"size": 425, "mtime": 1751073000018, "results": "8", "hashOfConfig": "7"}, {"size": 29631, "mtime": 1751076736708, "results": "9", "hashOfConfig": "7"}, {"size": 5373, "mtime": 1751076709995, "results": "10", "hashOfConfig": "7"}, {"size": 6577, "mtime": 1751078327374, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ncyc5o", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts", ["27"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx", [], [], {"ruleId": "28", "severity": 1, "message": "29", "line": 192, "column": 15, "nodeType": "30", "messageId": "31", "endLine": 192, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'data' is assigned a value but never used.", "Identifier", "unusedVar"]