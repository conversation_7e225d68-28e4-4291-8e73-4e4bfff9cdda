[{"C:\\Users\\<USER>\\lostandfound\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\Home.tsx": "5", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\components\\Navbar.tsx": "6", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\MyReports.tsx": "7", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\Login.tsx": "8", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\Register.tsx": "9", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\SearchLost.tsx": "10", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\ReportFound.tsx": "11", "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\lib\\supabase.ts": "12"}, {"size": 554, "mtime": 1751068407741, "results": "13", "hashOfConfig": "14"}, {"size": 425, "mtime": 1751068407608, "results": "15", "hashOfConfig": "14"}, {"size": 1155, "mtime": 1751068653734, "results": "16", "hashOfConfig": "14"}, {"size": 3594, "mtime": 1751068673057, "results": "17", "hashOfConfig": "14"}, {"size": 5022, "mtime": 1751068709162, "results": "18", "hashOfConfig": "14"}, {"size": 3306, "mtime": 1751068686493, "results": "19", "hashOfConfig": "14"}, {"size": 719, "mtime": 1751068762674, "results": "20", "hashOfConfig": "14"}, {"size": 2927, "mtime": 1751068723708, "results": "21", "hashOfConfig": "14"}, {"size": 4822, "mtime": 1751068811231, "results": "22", "hashOfConfig": "14"}, {"size": 453, "mtime": 1751068756021, "results": "23", "hashOfConfig": "14"}, {"size": 446, "mtime": 1751068750001, "results": "24", "hashOfConfig": "14"}, {"size": 1923, "mtime": 1751068627075, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lsij1u", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\MyReports.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\SearchLost.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\pages\\ReportFound.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\frontend\\src\\lib\\supabase.ts", [], []]