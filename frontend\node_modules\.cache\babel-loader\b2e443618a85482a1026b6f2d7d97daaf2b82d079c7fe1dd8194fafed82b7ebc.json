{"ast": null, "code": "import { version } from './version';\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n  JS_ENV = 'deno';\n} else if (typeof document !== 'undefined') {\n  JS_ENV = 'web';\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n  JS_ENV = 'react-native';\n} else {\n  JS_ENV = 'node';\n}\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `supabase-js-${JS_ENV}/${version}`\n};\nexport const DEFAULT_GLOBAL_OPTIONS = {\n  headers: DEFAULT_HEADERS\n};\nexport const DEFAULT_DB_OPTIONS = {\n  schema: 'public'\n};\nexport const DEFAULT_AUTH_OPTIONS = {\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  flowType: 'implicit'\n};\nexport const DEFAULT_REALTIME_OPTIONS = {};", "map": {"version": 3, "names": ["version", "JS_ENV", "<PERSON><PERSON>", "document", "navigator", "product", "DEFAULT_HEADERS", "DEFAULT_GLOBAL_OPTIONS", "headers", "DEFAULT_DB_OPTIONS", "schema", "DEFAULT_AUTH_OPTIONS", "autoRefreshToken", "persistSession", "detectSessionInUrl", "flowType", "DEFAULT_REALTIME_OPTIONS"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\supabase-js\\src\\lib\\constants.ts"], "sourcesContent": ["// constants.ts\nimport { RealtimeClientOptions } from '@supabase/realtime-js'\nimport { SupabaseAuthClientOptions } from './types'\nimport { version } from './version'\n\nlet JS_ENV = ''\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n  JS_ENV = 'deno'\n} else if (typeof document !== 'undefined') {\n  JS_ENV = 'web'\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n  JS_ENV = 'react-native'\n} else {\n  JS_ENV = 'node'\n}\n\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${version}` }\n\nexport const DEFAULT_GLOBAL_OPTIONS = {\n  headers: DEFAULT_HEADERS,\n}\n\nexport const DEFAULT_DB_OPTIONS = {\n  schema: 'public',\n}\n\nexport const DEFAULT_AUTH_OPTIONS: SupabaseAuthClientOptions = {\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  flowType: 'implicit',\n}\n\nexport const DEFAULT_REALTIME_OPTIONS: RealtimeClientOptions = {}\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,WAAW;AAEnC,IAAIC,MAAM,GAAG,EAAE;AACf;AACA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EAC/BD,MAAM,GAAG,MAAM;CAChB,MAAM,IAAI,OAAOE,QAAQ,KAAK,WAAW,EAAE;EAC1CF,MAAM,GAAG,KAAK;CACf,MAAM,IAAI,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAK,aAAa,EAAE;EAClFJ,MAAM,GAAG,cAAc;CACxB,MAAM;EACLA,MAAM,GAAG,MAAM;;AAGjB,OAAO,MAAMK,eAAe,GAAG;EAAE,eAAe,EAAE,eAAeL,MAAM,IAAID,OAAO;AAAE,CAAE;AAEtF,OAAO,MAAMO,sBAAsB,GAAG;EACpCC,OAAO,EAAEF;CACV;AAED,OAAO,MAAMG,kBAAkB,GAAG;EAChCC,MAAM,EAAE;CACT;AAED,OAAO,MAAMC,oBAAoB,GAA8B;EAC7DC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,kBAAkB,EAAE,IAAI;EACxBC,QAAQ,EAAE;CACX;AAED,OAAO,MAAMC,wBAAwB,GAA0B,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}