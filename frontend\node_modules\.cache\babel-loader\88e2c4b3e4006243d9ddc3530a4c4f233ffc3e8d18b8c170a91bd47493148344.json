{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [profile, setProfile] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // For demo purposes, just set loading to false\n    // In production, this would connect to Supabase\n    try {\n      // Get initial session\n      supabase.auth.getSession().then(({\n        data: {\n          session\n        }\n      }) => {\n        var _session$user;\n        setSession(session);\n        setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n        if (session !== null && session !== void 0 && session.user) {\n          fetchProfile(session.user.id);\n        }\n        setLoading(false);\n      }).catch(() => {\n        // If Supabase isn't configured, just set loading to false\n        setLoading(false);\n      });\n\n      // Listen for auth changes\n      const {\n        data: {\n          subscription\n        }\n      } = supabase.auth.onAuthStateChange(async (event, session) => {\n        var _session$user2;\n        setSession(session);\n        setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n        if (session !== null && session !== void 0 && session.user) {\n          await fetchProfile(session.user.id);\n        } else {\n          setProfile(null);\n        }\n        setLoading(false);\n      });\n      return () => subscription.unsubscribe();\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n      setLoading(false);\n    }\n  }, []);\n  const fetchProfile = async userId => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('profiles').select('*').eq('id', userId).single();\n      if (error) {\n        console.error('Error fetching profile:', error);\n        return;\n      }\n      setProfile(data);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n    }\n  };\n  const signUp = async (email, password, fullName) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName\n          }\n        }\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signIn = async (email, password) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signOut = async () => {\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n  const updateProfile = async updates => {\n    if (!user) return;\n    try {\n      const {\n        error\n      } = await supabase.from('profiles').update(updates).eq('id', user.id);\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n  const value = {\n    user,\n    profile,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"WnmrBce6rCS8rk0knAQE3r7MHV4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "supabase", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "profile", "setProfile", "session", "setSession", "loading", "setLoading", "auth", "getSession", "then", "data", "_session$user", "fetchProfile", "id", "catch", "subscription", "onAuthStateChange", "event", "_session$user2", "unsubscribe", "error", "console", "log", "userId", "from", "select", "eq", "single", "signUp", "email", "password", "fullName", "options", "full_name", "message", "signIn", "signInWithPassword", "signOut", "updateProfile", "updates", "update", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase, Profile } from '../lib/supabase';\n\ninterface AuthContextType {\n  user: User | null;\n  profile: Profile | null;\n  session: Session | null;\n  loading: boolean;\n  signUp: (email: string, password: string, fullName: string) => Promise<any>;\n  signIn: (email: string, password: string) => Promise<any>;\n  signOut: () => Promise<void>;\n  updateProfile: (updates: Partial<Profile>) => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // For demo purposes, just set loading to false\n    // In production, this would connect to Supabase\n    try {\n      // Get initial session\n      supabase.auth.getSession().then(({ data: { session } }) => {\n        setSession(session);\n        setUser(session?.user ?? null);\n        if (session?.user) {\n          fetchProfile(session.user.id);\n        }\n        setLoading(false);\n      }).catch(() => {\n        // If Supabase isn't configured, just set loading to false\n        setLoading(false);\n      });\n\n      // Listen for auth changes\n      const {\n        data: { subscription },\n      } = supabase.auth.onAuthStateChange(async (event, session) => {\n        setSession(session);\n        setUser(session?.user ?? null);\n\n        if (session?.user) {\n          await fetchProfile(session.user.id);\n        } else {\n          setProfile(null);\n        }\n        setLoading(false);\n      });\n\n      return () => subscription.unsubscribe();\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n      setLoading(false);\n    }\n  }, []);\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        console.error('Error fetching profile:', error);\n        return;\n      }\n\n      setProfile(data);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n    }\n  };\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      });\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return;\n\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .update(updates)\n        .eq('id', user.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    profile,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,QAAQ,QAAiB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAapD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACd;IACA;IACA,IAAI;MACF;MACAE,QAAQ,CAACoB,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QAAEC,IAAI,EAAE;UAAEP;QAAQ;MAAE,CAAC,KAAK;QAAA,IAAAQ,aAAA;QACzDP,UAAU,CAACD,OAAO,CAAC;QACnBH,OAAO,EAAAW,aAAA,GAACR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAY,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;QAC9B,IAAIR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;UACjBa,YAAY,CAACT,OAAO,CAACJ,IAAI,CAACc,EAAE,CAAC;QAC/B;QACAP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,CAACQ,KAAK,CAAC,MAAM;QACb;QACAR,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM;QACJI,IAAI,EAAE;UAAEK;QAAa;MACvB,CAAC,GAAG5B,QAAQ,CAACoB,IAAI,CAACS,iBAAiB,CAAC,OAAOC,KAAK,EAAEd,OAAO,KAAK;QAAA,IAAAe,cAAA;QAC5Dd,UAAU,CAACD,OAAO,CAAC;QACnBH,OAAO,EAAAkB,cAAA,GAACf,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAmB,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;QAE9B,IAAIf,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;UACjB,MAAMa,YAAY,CAACT,OAAO,CAACJ,IAAI,CAACc,EAAE,CAAC;QACrC,CAAC,MAAM;UACLX,UAAU,CAAC,IAAI,CAAC;QAClB;QACAI,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC;MAEF,OAAO,MAAMS,YAAY,CAACI,WAAW,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,YAAY,GAAG,MAAOW,MAAc,IAAK;IAC7C,IAAI;MACF,MAAM;QAAEb,IAAI;QAAEU;MAAM,CAAC,GAAG,MAAMjC,QAAQ,CACnCqC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEH,MAAM,CAAC,CAChBI,MAAM,CAAC,CAAC;MAEX,IAAIP,KAAK,EAAE;QACTC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;MACF;MAEAlB,UAAU,CAACQ,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMQ,MAAM,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,EAAEC,QAAgB,KAAK;IAC1E,IAAI;MACF,MAAM;QAAErB,IAAI;QAAEU;MAAM,CAAC,GAAG,MAAMjC,QAAQ,CAACoB,IAAI,CAACqB,MAAM,CAAC;QACjDC,KAAK;QACLC,QAAQ;QACRE,OAAO,EAAE;UACPtB,IAAI,EAAE;YACJuB,SAAS,EAAEF;UACb;QACF;MACF,CAAC,CAAC;MAEF,OAAO;QAAErB,IAAI;QAAEU;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEV,IAAI,EAAE,IAAI;QAAEU,KAAK,EAAE;UAAEc,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAON,KAAa,EAAEC,QAAgB,KAAK;IACxD,IAAI;MACF,MAAM;QAAEpB,IAAI;QAAEU;MAAM,CAAC,GAAG,MAAMjC,QAAQ,CAACoB,IAAI,CAAC6B,kBAAkB,CAAC;QAC7DP,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,OAAO;QAAEpB,IAAI;QAAEU;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEV,IAAI,EAAE,IAAI;QAAEU,KAAK,EAAE;UAAEc,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMG,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAEjB;MAAM,CAAC,GAAG,MAAMjC,QAAQ,CAACoB,IAAI,CAAC8B,OAAO,CAAC,CAAC;MAC/C,IAAIjB,KAAK,EAAE;QACTC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D;EACF,CAAC;EAED,MAAMgB,aAAa,GAAG,MAAOC,OAAyB,IAAK;IACzD,IAAI,CAACxC,IAAI,EAAE;IAEX,IAAI;MACF,MAAM;QAAEqB;MAAM,CAAC,GAAG,MAAMjC,QAAQ,CAC7BqC,IAAI,CAAC,UAAU,CAAC,CAChBgB,MAAM,CAACD,OAAO,CAAC,CACfb,EAAE,CAAC,IAAI,EAAE3B,IAAI,CAACc,EAAE,CAAC;MAEpB,IAAIO,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;;MAEA;MACA,MAAMR,YAAY,CAACb,IAAI,CAACc,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMqB,KAAK,GAAG;IACZ1C,IAAI;IACJE,OAAO;IACPE,OAAO;IACPE,OAAO;IACPuB,MAAM;IACNO,MAAM;IACNE,OAAO;IACPC;EACF,CAAC;EAED,oBACEjD,OAAA,CAACC,WAAW,CAACoD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5C,QAAA,EAChCA;EAAQ;IAAA8C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAChD,GAAA,CA/IWF,YAAqD;AAAAmD,EAAA,GAArDnD,YAAqD;AAAA,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}