import React, { useState } from 'react';
import { discService } from './lib/supabase';

type Page = 'home' | 'report-found' | 'search-lost' | 'login';

function App() {
  const [currentPage, setCurrentPage] = useState<Page>('home');

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <Home onNavigate={setCurrentPage} />;
      case 'report-found':
        return <ReportFound onNavigate={setCurrentPage} />;
      case 'search-lost':
        return <SearchLost onNavigate={setCurrentPage} />;
      case 'login':
        return <Login onNavigate={setCurrentPage} />;
      default:
        return <Home onNavigate={setCurrentPage} />;
    }
  };

  return (
    <div className="app">
      <nav className="navbar">
        <div className="nav-container">
          <div className="logo" onClick={() => setCurrentPage('home')} style={{ cursor: 'pointer' }}>
            DiscFinder
          </div>
          <div className="nav-buttons">
            <button className="nav-button" onClick={() => setCurrentPage('report-found')}>
              Report Found
            </button>
            <button className="nav-button" onClick={() => setCurrentPage('search-lost')}>
              Search Lost
            </button>
            <button className="nav-button primary" onClick={() => setCurrentPage('login')}>
              Sign Up
            </button>
          </div>
        </div>
      </nav>

      <main className="main-container">
        {renderPage()}
      </main>
    </div>
  );
}

interface PageProps {
  onNavigate: (page: Page) => void;
}

function Home({ onNavigate }: PageProps) {
  return (
    <div>
      <div className="hero">
        <h1>Lost Your Disc?</h1>
        <p>
          DiscFinder helps disc golf players reunite with their lost discs.
          Report found discs or search for your lost ones in our community database.
        </p>

        <div className="hero-buttons">
          <button className="hero-button primary" onClick={() => onNavigate('report-found')}>
            Report Found Disc
          </button>
          <button className="hero-button secondary" onClick={() => onNavigate('search-lost')}>
            Search Lost Discs
          </button>
        </div>
      </div>

      <div className="features">
        <div className="feature-card">
          <div className="feature-icon">
            <div>🔍</div>
          </div>
          <h3>Smart Matching</h3>
          <p>
            Our intelligent system matches found and lost discs based on brand, model, color, and location.
          </p>
        </div>

        <div className="feature-card">
          <div className="feature-icon">
            <div>📍</div>
          </div>
          <h3>Location Based</h3>
          <p>
            Find discs near where you lost them with our location-based search and matching.
          </p>
        </div>

        <div className="feature-card">
          <div className="feature-icon">
            <div>💬</div>
          </div>
          <h3>Easy Communication</h3>
          <p>
            Connect directly with finders and owners through our secure messaging system.
          </p>
        </div>
      </div>

      <div className="stats">
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-number">500+</div>
            <div className="stat-label">Discs Reunited</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">1,200+</div>
            <div className="stat-label">Active Users</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">95%</div>
            <div className="stat-label">Success Rate</div>
          </div>
        </div>
      </div>

      <div className="cta">
        <h2>Join the Community</h2>
        <p>
          Create an account to report found discs, search for lost ones, and help fellow disc golfers.
        </p>
        <button className="cta-button" onClick={() => onNavigate('login')}>
          Sign Up Now
        </button>
      </div>
    </div>
  );
}

function ReportFound({ onNavigate }: PageProps) {
  const [formData, setFormData] = useState({
    brand: '',
    model: '',
    discType: '',
    color: '',
    weight: '',
    condition: 'good',
    plasticType: '',
    stampText: '',
    phoneNumber: '',
    nameOnDisc: '',
    locationFound: '',
    foundDate: '',
    description: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      // Test connection first
      const { connected } = await discService.testConnection();

      if (!connected) {
        setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');
        console.log('Form data:', formData);
        setTimeout(() => {
          onNavigate('home');
        }, 2000);
        return;
      }

      // Prepare data for Supabase
      const discData = {
        brand: formData.brand,
        model: formData.model || undefined,
        disc_type: (formData.discType as 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver') || undefined,
        color: formData.color,
        weight: formData.weight ? parseInt(formData.weight) : undefined,
        condition: formData.condition as 'new' | 'excellent' | 'good' | 'fair' | 'poor',
        plastic_type: formData.plasticType || undefined,
        stamp_text: formData.stampText || undefined,
        phone_number: formData.phoneNumber || undefined,
        name_on_disc: formData.nameOnDisc || undefined,
        location_found: formData.locationFound,
        found_date: formData.foundDate,
        description: formData.description || undefined,
      };

      const { data, error } = await discService.createFoundDisc(discData);

      if (error) {
        setSubmitMessage(`Error: ${(error as any)?.message || 'Unknown error occurred'}`);
      } else {
        setSubmitMessage('Found disc reported successfully!');
        console.log('Saved disc:', data);
        setTimeout(() => {
          onNavigate('home');
        }, 2000);
      }
    } catch (error) {
      setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');
      console.log('Form data:', formData);
      setTimeout(() => {
        onNavigate('home');
      }, 2000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="form-container">
      <div className="form-header">
        <button className="back-button" onClick={() => onNavigate('home')}>
          ← Back to Home
        </button>
        <h1>Report Found Disc</h1>
        <p>Help reunite a disc with its owner by providing details about the disc you found.</p>
      </div>

      {submitMessage && (
        <div className={`status-message ${submitMessage.includes('Error') ? 'error' : 'success'}`}>
          {submitMessage}
        </div>
      )}

      <form className="disc-form" onSubmit={handleSubmit}>
        <div className="form-section">
          <h3>Disc Information</h3>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="brand">Brand *</label>
              <input
                type="text"
                id="brand"
                name="brand"
                value={formData.brand}
                onChange={handleInputChange}
                required
                placeholder="e.g., Innova, Discraft, Dynamic Discs"
              />
            </div>
            <div className="form-group">
              <label htmlFor="model">Model</label>
              <input
                type="text"
                id="model"
                name="model"
                value={formData.model}
                onChange={handleInputChange}
                placeholder="e.g., Destroyer, Buzzz, Judge"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="discType">Disc Type</label>
              <select
                id="discType"
                name="discType"
                value={formData.discType}
                onChange={handleInputChange}
              >
                <option value="">Select type</option>
                <option value="putter">Putter</option>
                <option value="midrange">Midrange</option>
                <option value="fairway_driver">Fairway Driver</option>
                <option value="distance_driver">Distance Driver</option>
                <option value="approach">Approach</option>
              </select>
            </div>
            <div className="form-group">
              <label htmlFor="color">Color *</label>
              <input
                type="text"
                id="color"
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                required
                placeholder="e.g., Blue, Red, Orange"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="weight">Weight (grams)</label>
              <input
                type="number"
                id="weight"
                name="weight"
                value={formData.weight}
                onChange={handleInputChange}
                placeholder="e.g., 175"
                min="100"
                max="200"
              />
            </div>
            <div className="form-group">
              <label htmlFor="condition">Condition</label>
              <select
                id="condition"
                name="condition"
                value={formData.condition}
                onChange={handleInputChange}
              >
                <option value="new">New</option>
                <option value="excellent">Excellent</option>
                <option value="good">Good</option>
                <option value="fair">Fair</option>
                <option value="poor">Poor</option>
              </select>
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>Additional Details</h3>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="plasticType">Plastic Type</label>
              <input
                type="text"
                id="plasticType"
                name="plasticType"
                value={formData.plasticType}
                onChange={handleInputChange}
                placeholder="e.g., Champion, ESP, Lucid"
              />
            </div>
            <div className="form-group">
              <label htmlFor="stampText">Stamp/Text</label>
              <input
                type="text"
                id="stampText"
                name="stampText"
                value={formData.stampText}
                onChange={handleInputChange}
                placeholder="Any text or stamps on the disc"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="phoneNumber">Phone Number on Disc</label>
              <input
                type="tel"
                id="phoneNumber"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                placeholder="Phone number written on disc"
              />
            </div>
            <div className="form-group">
              <label htmlFor="nameOnDisc">Name on Disc</label>
              <input
                type="text"
                id="nameOnDisc"
                name="nameOnDisc"
                value={formData.nameOnDisc}
                onChange={handleInputChange}
                placeholder="Name written on disc"
              />
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>Location & Date</h3>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="locationFound">Location Found *</label>
              <input
                type="text"
                id="locationFound"
                name="locationFound"
                value={formData.locationFound}
                onChange={handleInputChange}
                required
                placeholder="e.g., Maple Hill Disc Golf Course, Hole 7"
              />
            </div>
            <div className="form-group">
              <label htmlFor="foundDate">Date Found *</label>
              <input
                type="date"
                id="foundDate"
                name="foundDate"
                value={formData.foundDate}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="description">Additional Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              placeholder="Any additional details about where or how you found the disc..."
            />
          </div>
        </div>

        <div className="form-actions">
          <button
            type="button"
            className="button secondary"
            onClick={() => onNavigate('home')}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="button primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Report Found Disc'}
          </button>
        </div>
      </form>
    </div>
  );
}

function SearchLost({ onNavigate }: PageProps) {
  return (
    <div className="page-container">
      <div className="page-header">
        <button className="back-button" onClick={() => onNavigate('home')}>
          ← Back to Home
        </button>
        <h1>Search Lost Discs</h1>
        <p>Search through reported found discs to see if someone has found your lost disc.</p>
      </div>
      <div className="coming-soon">
        <h2>Coming Soon!</h2>
        <p>The search functionality will be implemented next.</p>
      </div>
    </div>
  );
}

function Login({ onNavigate }: PageProps) {
  return (
    <div className="page-container">
      <div className="page-header">
        <button className="back-button" onClick={() => onNavigate('home')}>
          ← Back to Home
        </button>
        <h1>Sign Up / Login</h1>
        <p>Create an account or sign in to manage your disc reports.</p>
      </div>
      <div className="coming-soon">
        <h2>Coming Soon!</h2>
        <p>Authentication will be implemented next.</p>
      </div>
    </div>
  );
}

export default App;
