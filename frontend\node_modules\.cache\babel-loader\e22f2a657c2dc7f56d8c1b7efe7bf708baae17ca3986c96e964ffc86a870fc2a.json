{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\pages\\\\ReportFound.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportFound = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-2xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-900 mb-6\",\n      children: \"Report Found Disc\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 text-center py-8\",\n        children: \"Report Found Disc form will be implemented here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = ReportFound;\nexport default ReportFound;\nvar _c;\n$RefreshReg$(_c, \"ReportFound\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ReportFound", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/pages/ReportFound.tsx"], "sourcesContent": ["import React from 'react';\n\nconst ReportFound: React.FC = () => {\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Report Found Disc</h1>\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <p className=\"text-gray-600 text-center py-8\">\n          Report Found Disc form will be implemented here.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default ReportFound;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,oBACED,OAAA;IAAKE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCH,OAAA;MAAIE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5EP,OAAA;MAAKE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDH,OAAA;QAAGE,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAE9C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAXIP,WAAqB;AAa3B,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}