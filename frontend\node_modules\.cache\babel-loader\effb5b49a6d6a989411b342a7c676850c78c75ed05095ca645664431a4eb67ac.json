{"ast": null, "code": "export { StorageClient } from './StorageClient';\nexport * from './lib/types';\nexport * from './lib/errors';", "map": {"version": 3, "names": ["StorageClient"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\storage-js\\src\\index.ts"], "sourcesContent": ["export { StorageClient as StorageClient } from './StorageClient'\nexport * from './lib/types'\nexport * from './lib/errors'\n"], "mappings": "AAAA,SAASA,aAA8B,QAAQ,iBAAiB;AAChE,cAAc,aAAa;AAC3B,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}