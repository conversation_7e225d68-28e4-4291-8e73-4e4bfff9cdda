{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [profile, setProfile] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      var _session$user;\n      setSession(session);\n      setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n      if (session !== null && session !== void 0 && session.user) {\n        fetchProfile(session.user.id);\n      }\n      setLoading(false);\n    });\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      var _session$user2;\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      if (session !== null && session !== void 0 && session.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n      }\n      setLoading(false);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const fetchProfile = async userId => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('profiles').select('*').eq('id', userId).single();\n      if (error) {\n        console.error('Error fetching profile:', error);\n        return;\n      }\n      setProfile(data);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n    }\n  };\n  const signUp = async (email, password, fullName) => {\n    const {\n      data,\n      error\n    } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName\n        }\n      }\n    });\n    return {\n      data,\n      error\n    };\n  };\n  const signIn = async (email, password) => {\n    const {\n      data,\n      error\n    } = await supabase.auth.signInWithPassword({\n      email,\n      password\n    });\n    return {\n      data,\n      error\n    };\n  };\n  const signOut = async () => {\n    const {\n      error\n    } = await supabase.auth.signOut();\n    if (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n  const updateProfile = async updates => {\n    if (!user) return;\n    try {\n      const {\n        error\n      } = await supabase.from('profiles').update(updates).eq('id', user.id);\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n  const value = {\n    user,\n    profile,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"WnmrBce6rCS8rk0knAQE3r7MHV4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "supabase", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "profile", "setProfile", "session", "setSession", "loading", "setLoading", "auth", "getSession", "then", "data", "_session$user", "fetchProfile", "id", "subscription", "onAuthStateChange", "event", "_session$user2", "unsubscribe", "userId", "error", "from", "select", "eq", "single", "console", "signUp", "email", "password", "fullName", "options", "full_name", "signIn", "signInWithPassword", "signOut", "updateProfile", "updates", "update", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase, Profile } from '../lib/supabase';\n\ninterface AuthContextType {\n  user: User | null;\n  profile: Profile | null;\n  session: Session | null;\n  loading: boolean;\n  signUp: (email: string, password: string, fullName: string) => Promise<any>;\n  signIn: (email: string, password: string) => Promise<any>;\n  signOut: () => Promise<void>;\n  updateProfile: (updates: Partial<Profile>) => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      if (session?.user) {\n        fetchProfile(session.user.id);\n      }\n      setLoading(false);\n    });\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n      }\n      setLoading(false);\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        console.error('Error fetching profile:', error);\n        return;\n      }\n\n      setProfile(data);\n    } catch (error) {\n      console.error('Error fetching profile:', error);\n    }\n  };\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    });\n\n    return { data, error };\n  };\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    });\n\n    return { data, error };\n  };\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return;\n\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .update(updates)\n        .eq('id', user.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    profile,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,QAAQ,QAAiB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAapD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACd;IACAE,QAAQ,CAACoB,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE;QAAEP;MAAQ;IAAE,CAAC,KAAK;MAAA,IAAAQ,aAAA;MACzDP,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAAW,aAAA,GAACR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAY,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;MAC9B,IAAIR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;QACjBa,YAAY,CAACT,OAAO,CAACJ,IAAI,CAACc,EAAE,CAAC;MAC/B;MACAP,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;;IAEF;IACA,MAAM;MACJI,IAAI,EAAE;QAAEI;MAAa;IACvB,CAAC,GAAG3B,QAAQ,CAACoB,IAAI,CAACQ,iBAAiB,CAAC,OAAOC,KAAK,EAAEb,OAAO,KAAK;MAAA,IAAAc,cAAA;MAC5Db,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAAiB,cAAA,GAACd,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAkB,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAE9B,IAAId,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;QACjB,MAAMa,YAAY,CAACT,OAAO,CAACJ,IAAI,CAACc,EAAE,CAAC;MACrC,CAAC,MAAM;QACLX,UAAU,CAAC,IAAI,CAAC;MAClB;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;IAEF,OAAO,MAAMQ,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMN,YAAY,GAAG,MAAOO,MAAc,IAAK;IAC7C,IAAI;MACF,MAAM;QAAET,IAAI;QAAEU;MAAM,CAAC,GAAG,MAAMjC,QAAQ,CACnCkC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEJ,MAAM,CAAC,CAChBK,MAAM,CAAC,CAAC;MAEX,IAAIJ,KAAK,EAAE;QACTK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;MACF;MAEAlB,UAAU,CAACQ,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMM,MAAM,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,EAAEC,QAAgB,KAAK;IAC1E,MAAM;MAAEnB,IAAI;MAAEU;IAAM,CAAC,GAAG,MAAMjC,QAAQ,CAACoB,IAAI,CAACmB,MAAM,CAAC;MACjDC,KAAK;MACLC,QAAQ;MACRE,OAAO,EAAE;QACPpB,IAAI,EAAE;UACJqB,SAAS,EAAEF;QACb;MACF;IACF,CAAC,CAAC;IAEF,OAAO;MAAEnB,IAAI;MAAEU;IAAM,CAAC;EACxB,CAAC;EAED,MAAMY,MAAM,GAAG,MAAAA,CAAOL,KAAa,EAAEC,QAAgB,KAAK;IACxD,MAAM;MAAElB,IAAI;MAAEU;IAAM,CAAC,GAAG,MAAMjC,QAAQ,CAACoB,IAAI,CAAC0B,kBAAkB,CAAC;MAC7DN,KAAK;MACLC;IACF,CAAC,CAAC;IAEF,OAAO;MAAElB,IAAI;MAAEU;IAAM,CAAC;EACxB,CAAC;EAED,MAAMc,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMjC,QAAQ,CAACoB,IAAI,CAAC2B,OAAO,CAAC,CAAC;IAC/C,IAAId,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF,CAAC;EAED,MAAMe,aAAa,GAAG,MAAOC,OAAyB,IAAK;IACzD,IAAI,CAACrC,IAAI,EAAE;IAEX,IAAI;MACF,MAAM;QAAEqB;MAAM,CAAC,GAAG,MAAMjC,QAAQ,CAC7BkC,IAAI,CAAC,UAAU,CAAC,CAChBgB,MAAM,CAACD,OAAO,CAAC,CACfb,EAAE,CAAC,IAAI,EAAExB,IAAI,CAACc,EAAE,CAAC;MAEpB,IAAIO,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;;MAEA;MACA,MAAMR,YAAY,CAACb,IAAI,CAACc,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMkB,KAAK,GAAG;IACZvC,IAAI;IACJE,OAAO;IACPE,OAAO;IACPE,OAAO;IACPqB,MAAM;IACNM,MAAM;IACNE,OAAO;IACPC;EACF,CAAC;EAED,oBACE9C,OAAA,CAACC,WAAW,CAACiD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzC,QAAA,EAChCA;EAAQ;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC7C,GAAA,CAzHWF,YAAqD;AAAAgD,EAAA,GAArDhD,YAAqD;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}