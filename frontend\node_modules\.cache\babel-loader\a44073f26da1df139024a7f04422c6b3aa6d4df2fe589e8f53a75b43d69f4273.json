{"ast": null, "code": "import { DEFAULT_TIMEOUT } from '../lib/constants';\nexport default class Push {\n  /**\n   * Initializes the Push\n   *\n   * @param channel The Channel\n   * @param event The event, for example `\"phx_join\"`\n   * @param payload The payload, for example `{user_id: 123}`\n   * @param timeout The push timeout in milliseconds\n   */\n  constructor(channel, event, payload = {}, timeout = DEFAULT_TIMEOUT) {\n    this.channel = channel;\n    this.event = event;\n    this.payload = payload;\n    this.timeout = timeout;\n    this.sent = false;\n    this.timeoutTimer = undefined;\n    this.ref = '';\n    this.receivedResp = null;\n    this.recHooks = [];\n    this.refEvent = null;\n  }\n  resend(timeout) {\n    this.timeout = timeout;\n    this._cancelRefEvent();\n    this.ref = '';\n    this.refEvent = null;\n    this.receivedResp = null;\n    this.sent = false;\n    this.send();\n  }\n  send() {\n    if (this._hasReceived('timeout')) {\n      return;\n    }\n    this.startTimeout();\n    this.sent = true;\n    this.channel.socket.push({\n      topic: this.channel.topic,\n      event: this.event,\n      payload: this.payload,\n      ref: this.ref,\n      join_ref: this.channel._joinRef()\n    });\n  }\n  updatePayload(payload) {\n    this.payload = Object.assign(Object.assign({}, this.payload), payload);\n  }\n  receive(status, callback) {\n    var _a;\n    if (this._hasReceived(status)) {\n      callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n    }\n    this.recHooks.push({\n      status,\n      callback\n    });\n    return this;\n  }\n  startTimeout() {\n    if (this.timeoutTimer) {\n      return;\n    }\n    this.ref = this.channel.socket._makeRef();\n    this.refEvent = this.channel._replyEventName(this.ref);\n    const callback = payload => {\n      this._cancelRefEvent();\n      this._cancelTimeout();\n      this.receivedResp = payload;\n      this._matchReceive(payload);\n    };\n    this.channel._on(this.refEvent, {}, callback);\n    this.timeoutTimer = setTimeout(() => {\n      this.trigger('timeout', {});\n    }, this.timeout);\n  }\n  trigger(status, response) {\n    if (this.refEvent) this.channel._trigger(this.refEvent, {\n      status,\n      response\n    });\n  }\n  destroy() {\n    this._cancelRefEvent();\n    this._cancelTimeout();\n  }\n  _cancelRefEvent() {\n    if (!this.refEvent) {\n      return;\n    }\n    this.channel._off(this.refEvent, {});\n  }\n  _cancelTimeout() {\n    clearTimeout(this.timeoutTimer);\n    this.timeoutTimer = undefined;\n  }\n  _matchReceive({\n    status,\n    response\n  }) {\n    this.recHooks.filter(h => h.status === status).forEach(h => h.callback(response));\n  }\n  _hasReceived(status) {\n    return this.receivedResp && this.receivedResp.status === status;\n  }\n}", "map": {"version": 3, "names": ["DEFAULT_TIMEOUT", "<PERSON><PERSON>", "constructor", "channel", "event", "payload", "timeout", "sent", "timeoutTimer", "undefined", "ref", "receivedResp", "rec<PERSON>ooks", "refEvent", "resend", "_cancelRefEvent", "send", "_hasReceived", "startTimeout", "socket", "push", "topic", "join_ref", "_joinRef", "updatePayload", "Object", "assign", "receive", "status", "callback", "_a", "response", "_makeRef", "_replyEventName", "_cancelTimeout", "_matchReceive", "_on", "setTimeout", "trigger", "_trigger", "destroy", "_off", "clearTimeout", "filter", "h", "for<PERSON>ach"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\realtime-js\\src\\lib\\push.ts"], "sourcesContent": ["import { DEFAULT_TIMEOUT } from '../lib/constants'\nimport type RealtimeChannel from '../RealtimeChannel'\n\nexport default class Push {\n  sent: boolean = false\n  timeoutTimer: number | undefined = undefined\n  ref: string = ''\n  receivedResp: {\n    status: string\n    response: { [key: string]: any }\n  } | null = null\n  recHooks: {\n    status: string\n    callback: Function\n  }[] = []\n  refEvent: string | null = null\n\n  /**\n   * Initializes the Push\n   *\n   * @param channel The Channel\n   * @param event The event, for example `\"phx_join\"`\n   * @param payload The payload, for example `{user_id: 123}`\n   * @param timeout The push timeout in milliseconds\n   */\n  constructor(\n    public channel: RealtimeChannel,\n    public event: string,\n    public payload: { [key: string]: any } = {},\n    public timeout: number = DEFAULT_TIMEOUT\n  ) {}\n\n  resend(timeout: number) {\n    this.timeout = timeout\n    this._cancelRefEvent()\n    this.ref = ''\n    this.refEvent = null\n    this.receivedResp = null\n    this.sent = false\n    this.send()\n  }\n\n  send() {\n    if (this._hasReceived('timeout')) {\n      return\n    }\n    this.startTimeout()\n    this.sent = true\n    this.channel.socket.push({\n      topic: this.channel.topic,\n      event: this.event,\n      payload: this.payload,\n      ref: this.ref,\n      join_ref: this.channel._joinRef(),\n    })\n  }\n\n  updatePayload(payload: { [key: string]: any }): void {\n    this.payload = { ...this.payload, ...payload }\n  }\n\n  receive(status: string, callback: Function) {\n    if (this._hasReceived(status)) {\n      callback(this.receivedResp?.response)\n    }\n\n    this.recHooks.push({ status, callback })\n    return this\n  }\n\n  startTimeout() {\n    if (this.timeoutTimer) {\n      return\n    }\n    this.ref = this.channel.socket._makeRef()\n    this.refEvent = this.channel._replyEventName(this.ref)\n\n    const callback = (payload: any) => {\n      this._cancelRefEvent()\n      this._cancelTimeout()\n      this.receivedResp = payload\n      this._matchReceive(payload)\n    }\n\n    this.channel._on(this.refEvent, {}, callback)\n\n    this.timeoutTimer = <any>setTimeout(() => {\n      this.trigger('timeout', {})\n    }, this.timeout)\n  }\n\n  trigger(status: string, response: any) {\n    if (this.refEvent)\n      this.channel._trigger(this.refEvent, { status, response })\n  }\n\n  destroy() {\n    this._cancelRefEvent()\n    this._cancelTimeout()\n  }\n\n  private _cancelRefEvent() {\n    if (!this.refEvent) {\n      return\n    }\n\n    this.channel._off(this.refEvent, {})\n  }\n\n  private _cancelTimeout() {\n    clearTimeout(this.timeoutTimer)\n    this.timeoutTimer = undefined\n  }\n\n  private _matchReceive({\n    status,\n    response,\n  }: {\n    status: string\n    response: Function\n  }) {\n    this.recHooks\n      .filter((h) => h.status === status)\n      .forEach((h) => h.callback(response))\n  }\n\n  private _hasReceived(status: string) {\n    return this.receivedResp && this.receivedResp.status === status\n  }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kBAAkB;AAGlD,eAAc,MAAOC,IAAI;EAcvB;;;;;;;;EAQAC,YACSC,OAAwB,EACxBC,KAAa,EACbC,OAAA,GAAkC,EAAE,EACpCC,OAAA,GAAkBN,eAAe;IAHjC,KAAAG,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,OAAO,GAAPA,OAAO;IAzBhB,KAAAC,IAAI,GAAY,KAAK;IACrB,KAAAC,YAAY,GAAuBC,SAAS;IAC5C,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,YAAY,GAGD,IAAI;IACf,KAAAC,QAAQ,GAGF,EAAE;IACR,KAAAC,QAAQ,GAAkB,IAAI;EAe3B;EAEHC,MAAMA,CAACR,OAAe;IACpB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACS,eAAe,EAAE;IACtB,IAAI,CAACL,GAAG,GAAG,EAAE;IACb,IAAI,CAACG,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,IAAI,GAAG,KAAK;IACjB,IAAI,CAACS,IAAI,EAAE;EACb;EAEAA,IAAIA,CAAA;IACF,IAAI,IAAI,CAACC,YAAY,CAAC,SAAS,CAAC,EAAE;MAChC;IACF;IACA,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACX,IAAI,GAAG,IAAI;IAChB,IAAI,CAACJ,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC;MACvBC,KAAK,EAAE,IAAI,CAAClB,OAAO,CAACkB,KAAK;MACzBjB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBK,GAAG,EAAE,IAAI,CAACA,GAAG;MACbY,QAAQ,EAAE,IAAI,CAACnB,OAAO,CAACoB,QAAQ;KAChC,CAAC;EACJ;EAEAC,aAAaA,CAACnB,OAA+B;IAC3C,IAAI,CAACA,OAAO,GAAAoB,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAACrB,OAAO,GAAKA,OAAO,CAAE;EAChD;EAEAsB,OAAOA,CAACC,MAAc,EAAEC,QAAkB;;IACxC,IAAI,IAAI,CAACZ,YAAY,CAACW,MAAM,CAAC,EAAE;MAC7BC,QAAQ,CAAC,CAAAC,EAAA,OAAI,CAACnB,YAAY,cAAAmB,EAAA,uBAAAA,EAAA,CAAEC,QAAQ,CAAC;IACvC;IAEA,IAAI,CAACnB,QAAQ,CAACQ,IAAI,CAAC;MAAEQ,MAAM;MAAEC;IAAQ,CAAE,CAAC;IACxC,OAAO,IAAI;EACb;EAEAX,YAAYA,CAAA;IACV,IAAI,IAAI,CAACV,YAAY,EAAE;MACrB;IACF;IACA,IAAI,CAACE,GAAG,GAAG,IAAI,CAACP,OAAO,CAACgB,MAAM,CAACa,QAAQ,EAAE;IACzC,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAACV,OAAO,CAAC8B,eAAe,CAAC,IAAI,CAACvB,GAAG,CAAC;IAEtD,MAAMmB,QAAQ,GAAIxB,OAAY,IAAI;MAChC,IAAI,CAACU,eAAe,EAAE;MACtB,IAAI,CAACmB,cAAc,EAAE;MACrB,IAAI,CAACvB,YAAY,GAAGN,OAAO;MAC3B,IAAI,CAAC8B,aAAa,CAAC9B,OAAO,CAAC;IAC7B,CAAC;IAED,IAAI,CAACF,OAAO,CAACiC,GAAG,CAAC,IAAI,CAACvB,QAAQ,EAAE,EAAE,EAAEgB,QAAQ,CAAC;IAE7C,IAAI,CAACrB,YAAY,GAAQ6B,UAAU,CAAC,MAAK;MACvC,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAChC,OAAO,CAAC;EAClB;EAEAgC,OAAOA,CAACV,MAAc,EAAEG,QAAa;IACnC,IAAI,IAAI,CAAClB,QAAQ,EACf,IAAI,CAACV,OAAO,CAACoC,QAAQ,CAAC,IAAI,CAAC1B,QAAQ,EAAE;MAAEe,MAAM;MAAEG;IAAQ,CAAE,CAAC;EAC9D;EAEAS,OAAOA,CAAA;IACL,IAAI,CAACzB,eAAe,EAAE;IACtB,IAAI,CAACmB,cAAc,EAAE;EACvB;EAEQnB,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACF,QAAQ,EAAE;MAClB;IACF;IAEA,IAAI,CAACV,OAAO,CAACsC,IAAI,CAAC,IAAI,CAAC5B,QAAQ,EAAE,EAAE,CAAC;EACtC;EAEQqB,cAAcA,CAAA;IACpBQ,YAAY,CAAC,IAAI,CAAClC,YAAY,CAAC;IAC/B,IAAI,CAACA,YAAY,GAAGC,SAAS;EAC/B;EAEQ0B,aAAaA,CAAC;IACpBP,MAAM;IACNG;EAAQ,CAIT;IACC,IAAI,CAACnB,QAAQ,CACV+B,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAChB,MAAM,KAAKA,MAAM,CAAC,CAClCiB,OAAO,CAAED,CAAC,IAAKA,CAAC,CAACf,QAAQ,CAACE,QAAQ,CAAC,CAAC;EACzC;EAEQd,YAAYA,CAACW,MAAc;IACjC,OAAO,IAAI,CAACjB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACiB,MAAM,KAAKA,MAAM;EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}