{"ast": null, "code": "export { FunctionsClient } from './FunctionsClient';\nexport { FunctionsError, FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion } from './types';", "map": {"version": 3, "names": ["FunctionsClient", "FunctionsError", "FunctionsFetchError", "FunctionsHttpError", "FunctionsRelayError", "FunctionRegion"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\functions-js\\src\\index.ts"], "sourcesContent": ["export { FunctionsClient } from './FunctionsClient'\nexport {\n  type FunctionInvokeOptions,\n  FunctionsError,\n  FunctionsFetchError,\n  FunctionsHttpError,\n  FunctionsRelayError,\n  FunctionRegion,\n  type FunctionsResponse,\n} from './types'\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mBAAmB;AACnD,SAEEC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,mBAAmB,EACnBC,cAAc,QAET,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}