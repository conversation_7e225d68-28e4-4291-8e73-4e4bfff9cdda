import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://demo.supabase.co'
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'demo-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Profile {
  id: string
  email: string
  full_name?: string
  phone?: string
  created_at: string
  updated_at: string
}

export interface FoundDisc {
  id: string
  finder_id: string
  brand: string
  model?: string
  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'
  color: string
  weight?: number
  condition?: 'new' | 'excellent' | 'good' | 'fair' | 'poor'
  plastic_type?: string
  stamp_text?: string
  phone_number?: string
  name_on_disc?: string
  location_found: string
  location_coordinates?: { x: number; y: number }
  found_date: string
  description?: string
  image_urls?: string[]
  status: 'active' | 'claimed' | 'expired' | 'spam'
  created_at: string
  updated_at: string
}

export interface LostDisc {
  id: string
  owner_id: string
  brand: string
  model?: string
  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'
  color: string
  weight?: number
  plastic_type?: string
  stamp_text?: string
  location_lost: string
  location_coordinates?: { x: number; y: number }
  lost_date: string
  description?: string
  reward_offered?: number
  contact_preference: string
  status: 'active' | 'claimed' | 'expired' | 'spam'
  created_at: string
  updated_at: string
}

export interface DiscMatch {
  id: string
  found_disc_id: string
  lost_disc_id: string
  match_score: number
  status: 'potential' | 'confirmed' | 'rejected'
  finder_contacted_at?: string
  owner_contacted_at?: string
  created_at: string
  updated_at: string
  found_disc?: FoundDisc
  lost_disc?: LostDisc
}

// Helper functions for database operations
export const discService = {
  // Create a new found disc report
  async createFoundDisc(discData: Omit<FoundDisc, 'id' | 'created_at' | 'updated_at' | 'status'>) {
    try {
      const { data, error } = await supabase
        .from('found_discs')
        .insert([{
          ...discData,
          status: 'active'
        }])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error creating found disc:', error)
      return { data: null, error }
    }
  },

  // Get all active found discs
  async getFoundDiscs() {
    try {
      const { data, error } = await supabase
        .from('found_discs')
        .select('*')
        .eq('status', 'active')
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error fetching found discs:', error)
      return { data: null, error }
    }
  },

  // Search found discs by criteria
  async searchFoundDiscs(searchCriteria: {
    brand?: string
    model?: string
    color?: string
    discType?: string
    locationFound?: string
  }) {
    try {
      let query = supabase
        .from('found_discs')
        .select('*')
        .eq('status', 'active')

      if (searchCriteria.brand) {
        query = query.ilike('brand', `%${searchCriteria.brand}%`)
      }
      if (searchCriteria.model) {
        query = query.ilike('model', `%${searchCriteria.model}%`)
      }
      if (searchCriteria.color) {
        query = query.ilike('color', `%${searchCriteria.color}%`)
      }
      if (searchCriteria.discType) {
        query = query.eq('disc_type', searchCriteria.discType)
      }
      if (searchCriteria.locationFound) {
        query = query.ilike('location_found', `%${searchCriteria.locationFound}%`)
      }

      const { data, error } = await query.order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error searching found discs:', error)
      return { data: null, error }
    }
  },

  // Test connection to Supabase
  async testConnection() {
    try {
      const { data, error } = await supabase
        .from('found_discs')
        .select('count')
        .limit(1)

      return { connected: !error, error }
    } catch (error) {
      return { connected: false, error }
    }
  }
}
