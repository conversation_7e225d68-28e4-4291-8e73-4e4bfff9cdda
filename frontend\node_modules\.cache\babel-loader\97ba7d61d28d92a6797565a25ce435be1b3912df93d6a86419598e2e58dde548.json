{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport ReportFound from './pages/ReportFound';\nimport SearchLost from './pages/SearchLost';\nimport MyReports from './pages/MyReports';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"container mx-auto px-4 py-8\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/report-found\",\n              element: /*#__PURE__*/_jsxDEV(ReportFound, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/search-lost\",\n              element: /*#__PURE__*/_jsxDEV(SearchLost, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/my-reports\",\n              element: /*#__PURE__*/_jsxDEV(MyReports, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Home", "ReportFound", "SearchLost", "MyReports", "<PERSON><PERSON>", "Register", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport ReportFound from './pages/ReportFound';\nimport SearchLost from './pages/SearchLost';\nimport MyReports from './pages/MyReports';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"min-h-screen bg-gray-50\">\n          <Navbar />\n          <main className=\"container mx-auto px-4 py-8\">\n            <Routes>\n              <Route path=\"/\" element={<Home />} />\n              <Route path=\"/report-found\" element={<ReportFound />} />\n              <Route path=\"/search-lost\" element={<SearchLost />} />\n              <Route path=\"/my-reports\" element={<MyReports />} />\n              <Route path=\"/login\" element={<Login />} />\n              <Route path=\"/register\" element={<Register />} />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACT,YAAY;IAAAW,QAAA,eACXF,OAAA,CAACZ,MAAM;MAAAc,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBACtCF,OAAA,CAACR,MAAM;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVP,OAAA;UAAMG,SAAS,EAAC,6BAA6B;UAAAD,QAAA,eAC3CF,OAAA,CAACX,MAAM;YAAAa,QAAA,gBACLF,OAAA,CAACV,KAAK;cAACkB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACP,IAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCP,OAAA,CAACV,KAAK;cAACkB,IAAI,EAAC,eAAe;cAACC,OAAO,eAAET,OAAA,CAACN,WAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDP,OAAA,CAACV,KAAK;cAACkB,IAAI,EAAC,cAAc;cAACC,OAAO,eAAET,OAAA,CAACL,UAAU;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDP,OAAA,CAACV,KAAK;cAACkB,IAAI,EAAC,aAAa;cAACC,OAAO,eAAET,OAAA,CAACJ,SAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDP,OAAA,CAACV,KAAK;cAACkB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAET,OAAA,CAACH,KAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CP,OAAA,CAACV,KAAK;cAACkB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACF,QAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACG,EAAA,GApBQT,GAAG;AAsBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}