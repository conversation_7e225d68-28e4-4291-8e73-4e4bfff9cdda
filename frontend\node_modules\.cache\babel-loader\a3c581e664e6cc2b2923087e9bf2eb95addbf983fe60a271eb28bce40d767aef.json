{"ast": null, "code": "import { WebSocket } from 'isows';\nimport { CHANNEL_EVENTS, CONNECTION_STATE, DEFAULT_VERSION, DEFAULT_TIMEOUT, SOCKET_STATES, TRANSPORTS, VSN, WS_CLOSE_NORMAL } from './lib/constants';\nimport Serializer from './lib/serializer';\nimport Timer from './lib/timer';\nimport { httpEndpointURL } from './lib/transformers';\nimport RealtimeChannel from './RealtimeChannel';\nconst noop = () => {};\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nexport default class RealtimeClient {\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers Deprecated: headers cannot be set on websocket connections and this option will be removed in the future.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.logLevel Sets the log level for Realtime\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint, options) {\n    var _a;\n    this.accessTokenValue = null;\n    this.apiKey = null;\n    this.channels = new Array();\n    this.endPoint = '';\n    this.httpEndpoint = '';\n    /** @deprecated headers cannot be set on websocket connections */\n    this.headers = {};\n    this.params = {};\n    this.timeout = DEFAULT_TIMEOUT;\n    this.heartbeatIntervalMs = 25000;\n    this.heartbeatTimer = undefined;\n    this.pendingHeartbeatRef = null;\n    this.heartbeatCallback = noop;\n    this.ref = 0;\n    this.logger = noop;\n    this.conn = null;\n    this.sendBuffer = [];\n    this.serializer = new Serializer();\n    this.stateChangeCallbacks = {\n      open: [],\n      close: [],\n      error: [],\n      message: []\n    };\n    this.accessToken = null;\n    /**\n     * Use either custom fetch, if provided, or default fetch to make HTTP requests\n     *\n     * @internal\n     */\n    this._resolveFetch = customFetch => {\n      let _fetch;\n      if (customFetch) {\n        _fetch = customFetch;\n      } else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({\n          default: fetch\n        }) => fetch(...args));\n      } else {\n        _fetch = fetch;\n      }\n      return (...args) => _fetch(...args);\n    };\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`;\n    this.httpEndpoint = httpEndpointURL(endPoint);\n    if (options === null || options === void 0 ? void 0 : options.transport) {\n      this.transport = options.transport;\n    } else {\n      this.transport = null;\n    }\n    if (options === null || options === void 0 ? void 0 : options.params) this.params = options.params;\n    if (options === null || options === void 0 ? void 0 : options.timeout) this.timeout = options.timeout;\n    if (options === null || options === void 0 ? void 0 : options.logger) this.logger = options.logger;\n    if ((options === null || options === void 0 ? void 0 : options.logLevel) || (options === null || options === void 0 ? void 0 : options.log_level)) {\n      this.logLevel = options.logLevel || options.log_level;\n      this.params = Object.assign(Object.assign({}, this.params), {\n        log_level: this.logLevel\n      });\n    }\n    if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs) this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n    const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue;\n      this.apiKey = accessTokenValue;\n    }\n    this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs) ? options.reconnectAfterMs : tries => {\n      return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n    };\n    this.encode = (options === null || options === void 0 ? void 0 : options.encode) ? options.encode : (payload, callback) => {\n      return callback(JSON.stringify(payload));\n    };\n    this.decode = (options === null || options === void 0 ? void 0 : options.decode) ? options.decode : this.serializer.decode.bind(this.serializer);\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect();\n      this.connect();\n    }, this.reconnectAfterMs);\n    this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n    if (options === null || options === void 0 ? void 0 : options.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported');\n      }\n      this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n      this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n    }\n    this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n  }\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect() {\n    if (this.conn) {\n      return;\n    }\n    if (!this.transport) {\n      this.transport = WebSocket;\n    }\n    if (!this.transport) {\n      throw new Error('No transport provided');\n    }\n    this.conn = new this.transport(this.endpointURL());\n    this.setupConnection();\n  }\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL() {\n    return this._appendParams(this.endPoint, Object.assign({}, this.params, {\n      vsn: VSN\n    }));\n  }\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code, reason) {\n    if (this.conn) {\n      this.conn.onclose = function () {}; // noop\n      if (code) {\n        this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n      } else {\n        this.conn.close();\n      }\n      this.conn = null;\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.reconnectTimer.reset();\n      this.channels.forEach(channel => channel.teardown());\n    }\n  }\n  /**\n   * Returns all created channels\n   */\n  getChannels() {\n    return this.channels;\n  }\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(channel) {\n    const status = await channel.unsubscribe();\n    if (this.channels.length === 0) {\n      this.disconnect();\n    }\n    return status;\n  }\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels() {\n    const values_1 = await Promise.all(this.channels.map(channel => channel.unsubscribe()));\n    this.channels = [];\n    this.disconnect();\n    return values_1;\n  }\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind, msg, data) {\n    this.logger(kind, msg, data);\n  }\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState() {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting;\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open;\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing;\n      default:\n        return CONNECTION_STATE.Closed;\n    }\n  }\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected() {\n    return this.connectionState() === CONNECTION_STATE.Open;\n  }\n  channel(topic, params = {\n    config: {}\n  }) {\n    const realtimeTopic = `realtime:${topic}`;\n    const exists = this.getChannels().find(c => c.topic === realtimeTopic);\n    if (!exists) {\n      const chan = new RealtimeChannel(`realtime:${topic}`, params, this);\n      this.channels.push(chan);\n      return chan;\n    } else {\n      return exists;\n    }\n  }\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data) {\n    const {\n      topic,\n      event,\n      payload,\n      ref\n    } = data;\n    const callback = () => {\n      this.encode(data, result => {\n        var _a;\n        (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n      });\n    };\n    this.log('push', `${topic} ${event} (${ref})`, payload);\n    if (this.isConnected()) {\n      callback();\n    } else {\n      this.sendBuffer.push(callback);\n    }\n  }\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token = null) {\n    let tokenToSend = token || this.accessToken && (await this.accessToken()) || this.accessTokenValue;\n    if (this.accessTokenValue != tokenToSend) {\n      this.accessTokenValue = tokenToSend;\n      this.channels.forEach(channel => {\n        const payload = {\n          access_token: tokenToSend,\n          version: DEFAULT_VERSION\n        };\n        tokenToSend && channel.updateJoinPayload(payload);\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend\n          });\n        }\n      });\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    var _a;\n    if (!this.isConnected()) {\n      this.heartbeatCallback('disconnected');\n      return;\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null;\n      this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n      this.heartbeatCallback('timeout');\n      (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(WS_CLOSE_NORMAL, 'hearbeat timeout');\n      return;\n    }\n    this.pendingHeartbeatRef = this._makeRef();\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef\n    });\n    this.heartbeatCallback('sent');\n    await this.setAuth();\n  }\n  onHeartbeat(callback) {\n    this.heartbeatCallback = callback;\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach(callback => callback());\n      this.sendBuffer = [];\n    }\n  }\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef() {\n    let newRef = this.ref + 1;\n    if (newRef === this.ref) {\n      this.ref = 0;\n    } else {\n      this.ref = newRef;\n    }\n    return this.ref.toString();\n  }\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic) {\n    let dupChannel = this.channels.find(c => c.topic === topic && (c._isJoined() || c._isJoining()));\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`);\n      dupChannel.unsubscribe();\n    }\n  }\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel) {\n    this.channels = this.channels.filter(c => c.topic !== channel.topic);\n  }\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  setupConnection() {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer';\n      this.conn.onopen = () => this._onConnOpen();\n      this.conn.onerror = error => this._onConnError(error);\n      this.conn.onmessage = event => this._onConnMessage(event);\n      this.conn.onclose = event => this._onConnClose(event);\n    }\n  }\n  /** @internal */\n  _onConnMessage(rawMessage) {\n    this.decode(rawMessage.data, msg => {\n      let {\n        topic,\n        event,\n        payload,\n        ref\n      } = msg;\n      if (topic === 'phoenix' && event === 'phx_reply') {\n        this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error');\n      }\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null;\n      }\n      this.log('receive', `${payload.status || ''} ${topic} ${event} ${ref && '(' + ref + ')' || ''}`, payload);\n      Array.from(this.channels).filter(channel => channel._isMember(topic)).forEach(channel => channel._trigger(event, payload, ref));\n      this.stateChangeCallbacks.message.forEach(callback => callback(msg));\n    });\n  }\n  /** @internal */\n  _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`);\n    this.flushSendBuffer();\n    this.reconnectTimer.reset();\n    if (!this.worker) {\n      this._startHeartbeat();\n    } else {\n      if (!this.workerRef) {\n        this._startWorkerHeartbeat();\n      }\n    }\n    this.stateChangeCallbacks.open.forEach(callback => callback());\n  }\n  /** @internal */\n  _startHeartbeat() {\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n    this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n  }\n  /** @internal */\n  _startWorkerHeartbeat() {\n    if (this.workerUrl) {\n      this.log('worker', `starting worker for from ${this.workerUrl}`);\n    } else {\n      this.log('worker', `starting default worker`);\n    }\n    const objectUrl = this._workerObjectUrl(this.workerUrl);\n    this.workerRef = new Worker(objectUrl);\n    this.workerRef.onerror = error => {\n      this.log('worker', 'worker error', error.message);\n      this.workerRef.terminate();\n    };\n    this.workerRef.onmessage = event => {\n      if (event.data.event === 'keepAlive') {\n        this.sendHeartbeat();\n      }\n    };\n    this.workerRef.postMessage({\n      event: 'start',\n      interval: this.heartbeatIntervalMs\n    });\n  }\n  /** @internal */\n  _onConnClose(event) {\n    this.log('transport', 'close', event);\n    this._triggerChanError();\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n    this.reconnectTimer.scheduleTimeout();\n    this.stateChangeCallbacks.close.forEach(callback => callback(event));\n  }\n  /** @internal */\n  _onConnError(error) {\n    this.log('transport', `${error}`);\n    this._triggerChanError();\n    this.stateChangeCallbacks.error.forEach(callback => callback(error));\n  }\n  /** @internal */\n  _triggerChanError() {\n    this.channels.forEach(channel => channel._trigger(CHANNEL_EVENTS.error));\n  }\n  /** @internal */\n  _appendParams(url, params) {\n    if (Object.keys(params).length === 0) {\n      return url;\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?';\n    const query = new URLSearchParams(params);\n    return `${url}${prefix}${query}`;\n  }\n  _workerObjectUrl(url) {\n    let result_url;\n    if (url) {\n      result_url = url;\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], {\n        type: 'application/javascript'\n      });\n      result_url = URL.createObjectURL(blob);\n    }\n    return result_url;\n  }\n}", "map": {"version": 3, "names": ["WebSocket", "CHANNEL_EVENTS", "CONNECTION_STATE", "DEFAULT_VERSION", "DEFAULT_TIMEOUT", "SOCKET_STATES", "TRANSPORTS", "VSN", "WS_CLOSE_NORMAL", "Serializer", "Timer", "httpEndpointURL", "RealtimeChannel", "noop", "WORKER_SCRIPT", "RealtimeClient", "constructor", "endPoint", "options", "accessTokenValue", "<PERSON><PERSON><PERSON><PERSON>", "channels", "Array", "httpEndpoint", "headers", "params", "timeout", "heartbeatIntervalMs", "heartbeatTimer", "undefined", "pendingHeartbeatRef", "heartbeat<PERSON><PERSON><PERSON>", "ref", "logger", "conn", "send<PERSON><PERSON><PERSON>", "serializer", "stateChangeCallbacks", "open", "close", "error", "message", "accessToken", "_resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default", "websocket", "transport", "logLevel", "log_level", "Object", "assign", "_a", "apikey", "reconnectAfterMs", "tries", "encode", "payload", "callback", "JSON", "stringify", "decode", "bind", "reconnectTimer", "disconnect", "connect", "worker", "window", "Worker", "Error", "workerUrl", "endpointURL", "setupConnection", "_appendParams", "vsn", "code", "reason", "onclose", "clearInterval", "reset", "for<PERSON>ach", "channel", "teardown", "getChannels", "removeChannel", "status", "unsubscribe", "length", "removeAllChannels", "values_1", "Promise", "all", "map", "log", "kind", "msg", "data", "connectionState", "readyState", "connecting", "Connecting", "Open", "closing", "Closing", "Closed", "isConnected", "topic", "config", "realtimeTopic", "exists", "find", "c", "chan", "push", "event", "result", "send", "setAuth", "token", "tokenToSend", "access_token", "version", "updateJoinPayload", "joinedOnce", "_isJoined", "_push", "sendHeartbeat", "_makeRef", "onHeartbeat", "flushSendBuffer", "newRef", "toString", "_leaveOpenTopic", "dup<PERSON><PERSON><PERSON>", "_isJoining", "_remove", "filter", "binaryType", "onopen", "_onConnOpen", "onerror", "_onConnError", "onmessage", "_onConnMessage", "_onConnClose", "rawMessage", "from", "_isMember", "_trigger", "_startHeartbeat", "workerRef", "_startWorkerHeartbeat", "setInterval", "objectUrl", "_workerObjectUrl", "terminate", "postMessage", "interval", "_trigger<PERSON>hanError", "scheduleTimeout", "url", "keys", "prefix", "match", "query", "URLSearchParams", "result_url", "blob", "Blob", "type", "URL", "createObjectURL"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\realtime-js\\src\\RealtimeClient.ts"], "sourcesContent": ["import { WebSocket } from 'isows'\n\nimport {\n  CHANNEL_EVENTS,\n  CONNECTION_STATE,\n  DEFAULT_VERSION,\n  DEFAULT_TIMEOUT,\n  SOCKET_STATES,\n  TRANSPORTS,\n  VSN,\n  WS_CLOSE_NORMAL,\n} from './lib/constants'\n\nimport Serializer from './lib/serializer'\nimport Timer from './lib/timer'\n\nimport { httpEndpointURL } from './lib/transformers'\nimport RealtimeChannel from './RealtimeChannel'\nimport type { RealtimeChannelOptions } from './RealtimeChannel'\n\ntype Fetch = typeof fetch\n\nexport type Channel = {\n  name: string\n  inserted_at: string\n  updated_at: string\n  id: number\n}\nexport type LogLevel = 'info' | 'warn' | 'error'\n\nexport type RealtimeMessage = {\n  topic: string\n  event: string\n  payload: any\n  ref: string\n  join_ref?: string\n}\n\nexport type RealtimeRemoveChannelResponse = 'ok' | 'timed out' | 'error'\nexport type HeartbeatStatus =\n  | 'sent'\n  | 'ok'\n  | 'error'\n  | 'timeout'\n  | 'disconnected'\n\nconst noop = () => {}\n\nexport interface WebSocketLikeConstructor {\n  new (\n    address: string | URL,\n    subprotocols?: string | string[] | undefined\n  ): WebSocketLike\n}\n\nexport type WebSocketLike = WebSocket\n\nexport interface WebSocketLikeError {\n  error: any\n  message: string\n  type: string\n}\n\nexport type RealtimeClientOptions = {\n  transport?: WebSocketLikeConstructor\n  timeout?: number\n  heartbeatIntervalMs?: number\n  logger?: Function\n  encode?: Function\n  decode?: Function\n  reconnectAfterMs?: Function\n  headers?: { [key: string]: string }\n  params?: { [key: string]: any }\n  //Deprecated: Use it in favour of correct casing `logLevel`\n  log_level?: LogLevel\n  logLevel?: LogLevel\n  fetch?: Fetch\n  worker?: boolean\n  workerUrl?: string\n  accessToken?: () => Promise<string | null>\n}\n\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`\n\nexport default class RealtimeClient {\n  accessTokenValue: string | null = null\n  apiKey: string | null = null\n  channels: RealtimeChannel[] = new Array()\n  endPoint: string = ''\n  httpEndpoint: string = ''\n  /** @deprecated headers cannot be set on websocket connections */\n  headers?: { [key: string]: string } = {}\n  params?: { [key: string]: string } = {}\n  timeout: number = DEFAULT_TIMEOUT\n  transport: WebSocketLikeConstructor | null\n  heartbeatIntervalMs: number = 25000\n  heartbeatTimer: ReturnType<typeof setInterval> | undefined = undefined\n  pendingHeartbeatRef: string | null = null\n  heartbeatCallback: (status: HeartbeatStatus) => void = noop\n  ref: number = 0\n  reconnectTimer: Timer\n  logger: Function = noop\n  logLevel?: LogLevel\n  encode: Function\n  decode: Function\n  reconnectAfterMs: Function\n  conn: WebSocketLike | null = null\n  sendBuffer: Function[] = []\n  serializer: Serializer = new Serializer()\n  stateChangeCallbacks: {\n    open: Function[]\n    close: Function[]\n    error: Function[]\n    message: Function[]\n  } = {\n    open: [],\n    close: [],\n    error: [],\n    message: [],\n  }\n  fetch: Fetch\n  accessToken: (() => Promise<string | null>) | null = null\n  worker?: boolean\n  workerUrl?: string\n  workerRef?: Worker\n\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers Deprecated: headers cannot be set on websocket connections and this option will be removed in the future.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.logLevel Sets the log level for Realtime\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint: string, options?: RealtimeClientOptions) {\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`\n    this.httpEndpoint = httpEndpointURL(endPoint)\n    if (options?.transport) {\n      this.transport = options.transport\n    } else {\n      this.transport = null\n    }\n    if (options?.params) this.params = options.params\n    if (options?.timeout) this.timeout = options.timeout\n    if (options?.logger) this.logger = options.logger\n    if (options?.logLevel || options?.log_level) {\n      this.logLevel = options.logLevel || options.log_level\n      this.params = { ...this.params, log_level: this.logLevel as string }\n    }\n\n    if (options?.heartbeatIntervalMs)\n      this.heartbeatIntervalMs = options.heartbeatIntervalMs\n\n    const accessTokenValue = options?.params?.apikey\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue\n      this.apiKey = accessTokenValue\n    }\n\n    this.reconnectAfterMs = options?.reconnectAfterMs\n      ? options.reconnectAfterMs\n      : (tries: number) => {\n          return [1000, 2000, 5000, 10000][tries - 1] || 10000\n        }\n    this.encode = options?.encode\n      ? options.encode\n      : (payload: JSON, callback: Function) => {\n          return callback(JSON.stringify(payload))\n        }\n    this.decode = options?.decode\n      ? options.decode\n      : this.serializer.decode.bind(this.serializer)\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect()\n      this.connect()\n    }, this.reconnectAfterMs)\n\n    this.fetch = this._resolveFetch(options?.fetch)\n    if (options?.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported')\n      }\n      this.worker = options?.worker || false\n      this.workerUrl = options?.workerUrl\n    }\n    this.accessToken = options?.accessToken || null\n  }\n\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect(): void {\n    if (this.conn) {\n      return\n    }\n    if (!this.transport) {\n      this.transport = WebSocket\n    }\n    if (!this.transport) {\n      throw new Error('No transport provided')\n    }\n    this.conn = new this.transport(this.endpointURL()) as WebSocketLike\n    this.setupConnection()\n  }\n\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL(): string {\n    return this._appendParams(\n      this.endPoint,\n      Object.assign({}, this.params, { vsn: VSN })\n    )\n  }\n\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code?: number, reason?: string): void {\n    if (this.conn) {\n      this.conn.onclose = function () {} // noop\n      if (code) {\n        this.conn.close(code, reason ?? '')\n      } else {\n        this.conn.close()\n      }\n      this.conn = null\n\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n      this.reconnectTimer.reset()\n      this.channels.forEach((channel) => channel.teardown())\n    }\n  }\n\n  /**\n   * Returns all created channels\n   */\n  getChannels(): RealtimeChannel[] {\n    return this.channels\n  }\n\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(\n    channel: RealtimeChannel\n  ): Promise<RealtimeRemoveChannelResponse> {\n    const status = await channel.unsubscribe()\n\n    if (this.channels.length === 0) {\n      this.disconnect()\n    }\n\n    return status\n  }\n\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels(): Promise<RealtimeRemoveChannelResponse[]> {\n    const values_1 = await Promise.all(\n      this.channels.map((channel) => channel.unsubscribe())\n    )\n    this.channels = []\n    this.disconnect()\n    return values_1\n  }\n\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind: string, msg: string, data?: any) {\n    this.logger(kind, msg, data)\n  }\n\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState(): CONNECTION_STATE {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing\n      default:\n        return CONNECTION_STATE.Closed\n    }\n  }\n\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected(): boolean {\n    return this.connectionState() === CONNECTION_STATE.Open\n  }\n\n  channel(\n    topic: string,\n    params: RealtimeChannelOptions = { config: {} }\n  ): RealtimeChannel {\n    const realtimeTopic = `realtime:${topic}`\n    const exists = this.getChannels().find(\n      (c: RealtimeChannel) => c.topic === realtimeTopic\n    )\n\n    if (!exists) {\n      const chan = new RealtimeChannel(`realtime:${topic}`, params, this)\n      this.channels.push(chan)\n\n      return chan\n    } else {\n      return exists\n    }\n  }\n\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data: RealtimeMessage): void {\n    const { topic, event, payload, ref } = data\n    const callback = () => {\n      this.encode(data, (result: any) => {\n        this.conn?.send(result)\n      })\n    }\n    this.log('push', `${topic} ${event} (${ref})`, payload)\n    if (this.isConnected()) {\n      callback()\n    } else {\n      this.sendBuffer.push(callback)\n    }\n  }\n\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token: string | null = null): Promise<void> {\n    let tokenToSend =\n      token ||\n      (this.accessToken && (await this.accessToken())) ||\n      this.accessTokenValue\n\n    if (this.accessTokenValue != tokenToSend) {\n      this.accessTokenValue = tokenToSend\n      this.channels.forEach((channel) => {\n        const payload = {\n          access_token: tokenToSend,\n          version: DEFAULT_VERSION,\n        }\n\n        tokenToSend && channel.updateJoinPayload(payload)\n\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend,\n          })\n        }\n      })\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    if (!this.isConnected()) {\n      this.heartbeatCallback('disconnected')\n      return\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null\n      this.log(\n        'transport',\n        'heartbeat timeout. Attempting to re-establish connection'\n      )\n      this.heartbeatCallback('timeout')\n      this.conn?.close(WS_CLOSE_NORMAL, 'hearbeat timeout')\n      return\n    }\n    this.pendingHeartbeatRef = this._makeRef()\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef,\n    })\n    this.heartbeatCallback('sent')\n    await this.setAuth()\n  }\n\n  onHeartbeat(callback: (status: HeartbeatStatus) => void): void {\n    this.heartbeatCallback = callback\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach((callback) => callback())\n      this.sendBuffer = []\n    }\n  }\n\n  /**\n   * Use either custom fetch, if provided, or default fetch to make HTTP requests\n   *\n   * @internal\n   */\n  _resolveFetch = (customFetch?: Fetch): Fetch => {\n    let _fetch: Fetch\n    if (customFetch) {\n      _fetch = customFetch\n    } else if (typeof fetch === 'undefined') {\n      _fetch = (...args) =>\n        import('@supabase/node-fetch' as any).then(({ default: fetch }) =>\n          fetch(...args)\n        )\n    } else {\n      _fetch = fetch\n    }\n    return (...args) => _fetch(...args)\n  }\n\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef(): string {\n    let newRef = this.ref + 1\n    if (newRef === this.ref) {\n      this.ref = 0\n    } else {\n      this.ref = newRef\n    }\n\n    return this.ref.toString()\n  }\n\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic: string): void {\n    let dupChannel = this.channels.find(\n      (c) => c.topic === topic && (c._isJoined() || c._isJoining())\n    )\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`)\n      dupChannel.unsubscribe()\n    }\n  }\n\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel: RealtimeChannel) {\n    this.channels = this.channels.filter((c) => c.topic !== channel.topic)\n  }\n\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  private setupConnection(): void {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer'\n      this.conn.onopen = () => this._onConnOpen()\n      this.conn.onerror = (error: Event) => this._onConnError(error)\n      this.conn.onmessage = (event: any) => this._onConnMessage(event)\n      this.conn.onclose = (event: any) => this._onConnClose(event)\n    }\n  }\n\n  /** @internal */\n  private _onConnMessage(rawMessage: { data: any }) {\n    this.decode(rawMessage.data, (msg: RealtimeMessage) => {\n      let { topic, event, payload, ref } = msg\n\n      if (topic === 'phoenix' && event === 'phx_reply') {\n        this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error')\n      }\n\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null\n      }\n\n      this.log(\n        'receive',\n        `${payload.status || ''} ${topic} ${event} ${\n          (ref && '(' + ref + ')') || ''\n        }`,\n        payload\n      )\n\n      Array.from(this.channels)\n        .filter((channel: RealtimeChannel) => channel._isMember(topic))\n        .forEach((channel: RealtimeChannel) =>\n          channel._trigger(event, payload, ref)\n        )\n\n      this.stateChangeCallbacks.message.forEach((callback) => callback(msg))\n    })\n  }\n\n  /** @internal */\n  private _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`)\n    this.flushSendBuffer()\n    this.reconnectTimer.reset()\n    if (!this.worker) {\n      this._startHeartbeat()\n    } else {\n      if (!this.workerRef) {\n        this._startWorkerHeartbeat()\n      }\n    }\n\n    this.stateChangeCallbacks.open.forEach((callback) => callback())\n  }\n  /** @internal */\n  private _startHeartbeat() {\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.heartbeatTimer = setInterval(\n      () => this.sendHeartbeat(),\n      this.heartbeatIntervalMs\n    )\n  }\n\n  /** @internal */\n  private _startWorkerHeartbeat() {\n    if (this.workerUrl) {\n      this.log('worker', `starting worker for from ${this.workerUrl}`)\n    } else {\n      this.log('worker', `starting default worker`)\n    }\n    const objectUrl = this._workerObjectUrl(this.workerUrl!)\n    this.workerRef = new Worker(objectUrl)\n    this.workerRef.onerror = (error) => {\n      this.log('worker', 'worker error', (error as ErrorEvent).message)\n      this.workerRef!.terminate()\n    }\n    this.workerRef.onmessage = (event) => {\n      if (event.data.event === 'keepAlive') {\n        this.sendHeartbeat()\n      }\n    }\n    this.workerRef.postMessage({\n      event: 'start',\n      interval: this.heartbeatIntervalMs,\n    })\n  }\n  /** @internal */\n  private _onConnClose(event: any) {\n    this.log('transport', 'close', event)\n    this._triggerChanError()\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.reconnectTimer.scheduleTimeout()\n    this.stateChangeCallbacks.close.forEach((callback) => callback(event))\n  }\n\n  /** @internal */\n  private _onConnError(error: Event) {\n    this.log('transport', `${error}`)\n    this._triggerChanError()\n    this.stateChangeCallbacks.error.forEach((callback) => callback(error))\n  }\n\n  /** @internal */\n  private _triggerChanError() {\n    this.channels.forEach((channel: RealtimeChannel) =>\n      channel._trigger(CHANNEL_EVENTS.error)\n    )\n  }\n\n  /** @internal */\n  private _appendParams(\n    url: string,\n    params: { [key: string]: string }\n  ): string {\n    if (Object.keys(params).length === 0) {\n      return url\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?'\n    const query = new URLSearchParams(params)\n    return `${url}${prefix}${query}`\n  }\n\n  private _workerObjectUrl(url: string | undefined): string {\n    let result_url: string\n    if (url) {\n      result_url = url\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' })\n      result_url = URL.createObjectURL(blob)\n    }\n    return result_url\n  }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AAEjC,SACEC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,UAAU,EACVC,GAAG,EACHC,eAAe,QACV,iBAAiB;AAExB,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,KAAK,MAAM,aAAa;AAE/B,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,eAAe,MAAM,mBAAmB;AA6B/C,MAAMC,IAAI,GAAGA,CAAA,KAAK,CAAE,CAAC;AAoCrB,MAAMC,aAAa,GAAG;;;;;MAKhB;AAEN,eAAc,MAAOC,cAAc;EA0CjC;;;;;;;;;;;;;;;;;;EAkBAC,YAAYC,QAAgB,EAAEC,OAA+B;;IA3D7D,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAAC,QAAQ,GAAsB,IAAIC,KAAK,EAAE;IACzC,KAAAL,QAAQ,GAAW,EAAE;IACrB,KAAAM,YAAY,GAAW,EAAE;IACzB;IACA,KAAAC,OAAO,GAA+B,EAAE;IACxC,KAAAC,MAAM,GAA+B,EAAE;IACvC,KAAAC,OAAO,GAAWtB,eAAe;IAEjC,KAAAuB,mBAAmB,GAAW,KAAK;IACnC,KAAAC,cAAc,GAA+CC,SAAS;IACtE,KAAAC,mBAAmB,GAAkB,IAAI;IACzC,KAAAC,iBAAiB,GAAsClB,IAAI;IAC3D,KAAAmB,GAAG,GAAW,CAAC;IAEf,KAAAC,MAAM,GAAapB,IAAI;IAKvB,KAAAqB,IAAI,GAAyB,IAAI;IACjC,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,UAAU,GAAe,IAAI3B,UAAU,EAAE;IACzC,KAAA4B,oBAAoB,GAKhB;MACFC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;KACV;IAED,KAAAC,WAAW,GAA0C,IAAI;IAqTzD;;;;;IAKA,KAAAC,aAAa,GAAIC,WAAmB,IAAW;MAC7C,IAAIC,MAAa;MACjB,IAAID,WAAW,EAAE;QACfC,MAAM,GAAGD,WAAW;MACtB,CAAC,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;QACvCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC,OAAO,EAAEH;QAAK,CAAE,KAC5DA,KAAK,CAAC,GAAGC,IAAI,CAAC,CACf;MACL,CAAC,MAAM;QACLF,MAAM,GAAGC,KAAK;MAChB;MACA,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;IACrC,CAAC;IA/SC,IAAI,CAAC9B,QAAQ,GAAG,GAAGA,QAAQ,IAAIX,UAAU,CAAC4C,SAAS,EAAE;IACrD,IAAI,CAAC3B,YAAY,GAAGZ,eAAe,CAACM,QAAQ,CAAC;IAC7C,IAAIC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiC,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAGjC,OAAO,CAACiC,SAAS;IACpC,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI;IACvB;IACA,IAAIjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGP,OAAO,CAACO,MAAM;IACjD,IAAIP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,OAAO,EAAE,IAAI,CAACA,OAAO,GAAGR,OAAO,CAACQ,OAAO;IACpD,IAAIR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEe,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGf,OAAO,CAACe,MAAM;IACjD,IAAI,CAAAf,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkC,QAAQ,MAAIlC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmC,SAAS,GAAE;MAC3C,IAAI,CAACD,QAAQ,GAAGlC,OAAO,CAACkC,QAAQ,IAAIlC,OAAO,CAACmC,SAAS;MACrD,IAAI,CAAC5B,MAAM,GAAA6B,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAC9B,MAAM;QAAE4B,SAAS,EAAE,IAAI,CAACD;MAAkB,EAAE;IACtE;IAEA,IAAIlC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,mBAAmB,EAC9B,IAAI,CAACA,mBAAmB,GAAGT,OAAO,CAACS,mBAAmB;IAExD,MAAMR,gBAAgB,GAAG,CAAAqC,EAAA,GAAAtC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,MAAM,cAAA+B,EAAA,uBAAAA,EAAA,CAAEC,MAAM;IAChD,IAAItC,gBAAgB,EAAE;MACpB,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,MAAM,GAAGD,gBAAgB;IAChC;IAEA,IAAI,CAACuC,gBAAgB,GAAG,CAAAxC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,gBAAgB,IAC7CxC,OAAO,CAACwC,gBAAgB,GACvBC,KAAa,IAAI;MAChB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACA,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK;IACtD,CAAC;IACL,IAAI,CAACC,MAAM,GAAG,CAAA1C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0C,MAAM,IACzB1C,OAAO,CAAC0C,MAAM,GACd,CAACC,OAAa,EAAEC,QAAkB,KAAI;MACpC,OAAOA,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC1C,CAAC;IACL,IAAI,CAACI,MAAM,GAAG,CAAA/C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+C,MAAM,IACzB/C,OAAO,CAAC+C,MAAM,GACd,IAAI,CAAC7B,UAAU,CAAC6B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC;IAChD,IAAI,CAAC+B,cAAc,GAAG,IAAIzD,KAAK,CAAC,YAAW;MACzC,IAAI,CAAC0D,UAAU,EAAE;MACjB,IAAI,CAACC,OAAO,EAAE;IAChB,CAAC,EAAE,IAAI,CAACX,gBAAgB,CAAC;IAEzB,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACH,aAAa,CAACzB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4B,KAAK,CAAC;IAC/C,IAAI5B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoD,MAAM,EAAE;MACnB,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACnD,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAChD;MACA,IAAI,CAACH,MAAM,GAAG,CAAApD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoD,MAAM,KAAI,KAAK;MACtC,IAAI,CAACI,SAAS,GAAGxD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwD,SAAS;IACrC;IACA,IAAI,CAAChC,WAAW,GAAG,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB,WAAW,KAAI,IAAI;EACjD;EAEA;;;EAGA2B,OAAOA,CAAA;IACL,IAAI,IAAI,CAACnC,IAAI,EAAE;MACb;IACF;IACA,IAAI,CAAC,IAAI,CAACiB,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAGnD,SAAS;IAC5B;IACA,IAAI,CAAC,IAAI,CAACmD,SAAS,EAAE;MACnB,MAAM,IAAIsB,KAAK,CAAC,uBAAuB,CAAC;IAC1C;IACA,IAAI,CAACvC,IAAI,GAAG,IAAI,IAAI,CAACiB,SAAS,CAAC,IAAI,CAACwB,WAAW,EAAE,CAAkB;IACnE,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;;EAIAD,WAAWA,CAAA;IACT,OAAO,IAAI,CAACE,aAAa,CACvB,IAAI,CAAC5D,QAAQ,EACbqC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC9B,MAAM,EAAE;MAAEqD,GAAG,EAAEvE;IAAG,CAAE,CAAC,CAC7C;EACH;EAEA;;;;;;EAMA6D,UAAUA,CAACW,IAAa,EAAEC,MAAe;IACvC,IAAI,IAAI,CAAC9C,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC+C,OAAO,GAAG,aAAa,CAAC,EAAC;MACnC,IAAIF,IAAI,EAAE;QACR,IAAI,CAAC7C,IAAI,CAACK,KAAK,CAACwC,IAAI,EAAEC,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QACL,IAAI,CAAC9C,IAAI,CAACK,KAAK,EAAE;MACnB;MACA,IAAI,CAACL,IAAI,GAAG,IAAI;MAEhB;MACA,IAAI,CAACN,cAAc,IAAIsD,aAAa,CAAC,IAAI,CAACtD,cAAc,CAAC;MACzD,IAAI,CAACuC,cAAc,CAACgB,KAAK,EAAE;MAC3B,IAAI,CAAC9D,QAAQ,CAAC+D,OAAO,CAAEC,OAAO,IAAKA,OAAO,CAACC,QAAQ,EAAE,CAAC;IACxD;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAAClE,QAAQ;EACtB;EAEA;;;;EAIA,MAAMmE,aAAaA,CACjBH,OAAwB;IAExB,MAAMI,MAAM,GAAG,MAAMJ,OAAO,CAACK,WAAW,EAAE;IAE1C,IAAI,IAAI,CAACrE,QAAQ,CAACsE,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACvB,UAAU,EAAE;IACnB;IAEA,OAAOqB,MAAM;EACf;EAEA;;;EAGA,MAAMG,iBAAiBA,CAAA;IACrB,MAAMC,QAAQ,GAAG,MAAMC,OAAO,CAACC,GAAG,CAChC,IAAI,CAAC1E,QAAQ,CAAC2E,GAAG,CAAEX,OAAO,IAAKA,OAAO,CAACK,WAAW,EAAE,CAAC,CACtD;IACD,IAAI,CAACrE,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC+C,UAAU,EAAE;IACjB,OAAOyB,QAAQ;EACjB;EAEA;;;;;EAKAI,GAAGA,CAACC,IAAY,EAAEC,GAAW,EAAEC,IAAU;IACvC,IAAI,CAACnE,MAAM,CAACiE,IAAI,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAC9B;EAEA;;;EAGAC,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACnE,IAAI,IAAI,IAAI,CAACA,IAAI,CAACoE,UAAU;MACvC,KAAKjG,aAAa,CAACkG,UAAU;QAC3B,OAAOrG,gBAAgB,CAACsG,UAAU;MACpC,KAAKnG,aAAa,CAACiC,IAAI;QACrB,OAAOpC,gBAAgB,CAACuG,IAAI;MAC9B,KAAKpG,aAAa,CAACqG,OAAO;QACxB,OAAOxG,gBAAgB,CAACyG,OAAO;MACjC;QACE,OAAOzG,gBAAgB,CAAC0G,MAAM;IAClC;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACR,eAAe,EAAE,KAAKnG,gBAAgB,CAACuG,IAAI;EACzD;EAEApB,OAAOA,CACLyB,KAAa,EACbrF,MAAA,GAAiC;IAAEsF,MAAM,EAAE;EAAE,CAAE;IAE/C,MAAMC,aAAa,GAAG,YAAYF,KAAK,EAAE;IACzC,MAAMG,MAAM,GAAG,IAAI,CAAC1B,WAAW,EAAE,CAAC2B,IAAI,CACnCC,CAAkB,IAAKA,CAAC,CAACL,KAAK,KAAKE,aAAa,CAClD;IAED,IAAI,CAACC,MAAM,EAAE;MACX,MAAMG,IAAI,GAAG,IAAIxG,eAAe,CAAC,YAAYkG,KAAK,EAAE,EAAErF,MAAM,EAAE,IAAI,CAAC;MACnE,IAAI,CAACJ,QAAQ,CAACgG,IAAI,CAACD,IAAI,CAAC;MAExB,OAAOA,IAAI;IACb,CAAC,MAAM;MACL,OAAOH,MAAM;IACf;EACF;EAEA;;;;;EAKAI,IAAIA,CAACjB,IAAqB;IACxB,MAAM;MAAEU,KAAK;MAAEQ,KAAK;MAAEzD,OAAO;MAAE7B;IAAG,CAAE,GAAGoE,IAAI;IAC3C,MAAMtC,QAAQ,GAAGA,CAAA,KAAK;MACpB,IAAI,CAACF,MAAM,CAACwC,IAAI,EAAGmB,MAAW,IAAI;;QAChC,CAAA/D,EAAA,OAAI,CAACtB,IAAI,cAAAsB,EAAA,uBAAAA,EAAA,CAAEgE,IAAI,CAACD,MAAM,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAACtB,GAAG,CAAC,MAAM,EAAE,GAAGa,KAAK,IAAIQ,KAAK,KAAKtF,GAAG,GAAG,EAAE6B,OAAO,CAAC;IACvD,IAAI,IAAI,CAACgD,WAAW,EAAE,EAAE;MACtB/C,QAAQ,EAAE;IACZ,CAAC,MAAM;MACL,IAAI,CAAC3B,UAAU,CAACkF,IAAI,CAACvD,QAAQ,CAAC;IAChC;EACF;EAEA;;;;;;;;;EASA,MAAM2D,OAAOA,CAACC,KAAA,GAAuB,IAAI;IACvC,IAAIC,WAAW,GACbD,KAAK,IACJ,IAAI,CAAChF,WAAW,KAAK,MAAM,IAAI,CAACA,WAAW,EAAE,CAAE,IAChD,IAAI,CAACvB,gBAAgB;IAEvB,IAAI,IAAI,CAACA,gBAAgB,IAAIwG,WAAW,EAAE;MACxC,IAAI,CAACxG,gBAAgB,GAAGwG,WAAW;MACnC,IAAI,CAACtG,QAAQ,CAAC+D,OAAO,CAAEC,OAAO,IAAI;QAChC,MAAMxB,OAAO,GAAG;UACd+D,YAAY,EAAED,WAAW;UACzBE,OAAO,EAAE1H;SACV;QAEDwH,WAAW,IAAItC,OAAO,CAACyC,iBAAiB,CAACjE,OAAO,CAAC;QAEjD,IAAIwB,OAAO,CAAC0C,UAAU,IAAI1C,OAAO,CAAC2C,SAAS,EAAE,EAAE;UAC7C3C,OAAO,CAAC4C,KAAK,CAAChI,cAAc,CAAC2H,YAAY,EAAE;YACzCA,YAAY,EAAED;WACf,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF;EACA;;;EAGA,MAAMO,aAAaA,CAAA;;IACjB,IAAI,CAAC,IAAI,CAACrB,WAAW,EAAE,EAAE;MACvB,IAAI,CAAC9E,iBAAiB,CAAC,cAAc,CAAC;MACtC;IACF;IACA,IAAI,IAAI,CAACD,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACmE,GAAG,CACN,WAAW,EACX,0DAA0D,CAC3D;MACD,IAAI,CAAClE,iBAAiB,CAAC,SAAS,CAAC;MACjC,CAAAyB,EAAA,OAAI,CAACtB,IAAI,cAAAsB,EAAA,uBAAAA,EAAA,CAAEjB,KAAK,CAAC/B,eAAe,EAAE,kBAAkB,CAAC;MACrD;IACF;IACA,IAAI,CAACsB,mBAAmB,GAAG,IAAI,CAACqG,QAAQ,EAAE;IAC1C,IAAI,CAACd,IAAI,CAAC;MACRP,KAAK,EAAE,SAAS;MAChBQ,KAAK,EAAE,WAAW;MAClBzD,OAAO,EAAE,EAAE;MACX7B,GAAG,EAAE,IAAI,CAACF;KACX,CAAC;IACF,IAAI,CAACC,iBAAiB,CAAC,MAAM,CAAC;IAC9B,MAAM,IAAI,CAAC0F,OAAO,EAAE;EACtB;EAEAW,WAAWA,CAACtE,QAA2C;IACrD,IAAI,CAAC/B,iBAAiB,GAAG+B,QAAQ;EACnC;EACA;;;EAGAuE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACxB,WAAW,EAAE,IAAI,IAAI,CAAC1E,UAAU,CAACwD,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACxD,UAAU,CAACiD,OAAO,CAAEtB,QAAQ,IAAKA,QAAQ,EAAE,CAAC;MACjD,IAAI,CAAC3B,UAAU,GAAG,EAAE;IACtB;EACF;EAsBA;;;;;EAKAgG,QAAQA,CAAA;IACN,IAAIG,MAAM,GAAG,IAAI,CAACtG,GAAG,GAAG,CAAC;IACzB,IAAIsG,MAAM,KAAK,IAAI,CAACtG,GAAG,EAAE;MACvB,IAAI,CAACA,GAAG,GAAG,CAAC;IACd,CAAC,MAAM;MACL,IAAI,CAACA,GAAG,GAAGsG,MAAM;IACnB;IAEA,OAAO,IAAI,CAACtG,GAAG,CAACuG,QAAQ,EAAE;EAC5B;EAEA;;;;;EAKAC,eAAeA,CAAC1B,KAAa;IAC3B,IAAI2B,UAAU,GAAG,IAAI,CAACpH,QAAQ,CAAC6F,IAAI,CAChCC,CAAC,IAAKA,CAAC,CAACL,KAAK,KAAKA,KAAK,KAAKK,CAAC,CAACa,SAAS,EAAE,IAAIb,CAAC,CAACuB,UAAU,EAAE,CAAC,CAC9D;IACD,IAAID,UAAU,EAAE;MACd,IAAI,CAACxC,GAAG,CAAC,WAAW,EAAE,4BAA4Ba,KAAK,GAAG,CAAC;MAC3D2B,UAAU,CAAC/C,WAAW,EAAE;IAC1B;EACF;EAEA;;;;;;;EAOAiD,OAAOA,CAACtD,OAAwB;IAC9B,IAAI,CAAChE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuH,MAAM,CAAEzB,CAAC,IAAKA,CAAC,CAACL,KAAK,KAAKzB,OAAO,CAACyB,KAAK,CAAC;EACxE;EAEA;;;;;EAKQlC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAAC1C,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC2G,UAAU,GAAG,aAAa;MACpC,IAAI,CAAC3G,IAAI,CAAC4G,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,EAAE;MAC3C,IAAI,CAAC7G,IAAI,CAAC8G,OAAO,GAAIxG,KAAY,IAAK,IAAI,CAACyG,YAAY,CAACzG,KAAK,CAAC;MAC9D,IAAI,CAACN,IAAI,CAACgH,SAAS,GAAI5B,KAAU,IAAK,IAAI,CAAC6B,cAAc,CAAC7B,KAAK,CAAC;MAChE,IAAI,CAACpF,IAAI,CAAC+C,OAAO,GAAIqC,KAAU,IAAK,IAAI,CAAC8B,YAAY,CAAC9B,KAAK,CAAC;IAC9D;EACF;EAEA;EACQ6B,cAAcA,CAACE,UAAyB;IAC9C,IAAI,CAACpF,MAAM,CAACoF,UAAU,CAACjD,IAAI,EAAGD,GAAoB,IAAI;MACpD,IAAI;QAAEW,KAAK;QAAEQ,KAAK;QAAEzD,OAAO;QAAE7B;MAAG,CAAE,GAAGmE,GAAG;MAExC,IAAIW,KAAK,KAAK,SAAS,IAAIQ,KAAK,KAAK,WAAW,EAAE;QAChD,IAAI,CAACvF,iBAAiB,CAACoE,GAAG,CAACtC,OAAO,CAAC4B,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC;MACrE;MAEA,IAAIzD,GAAG,IAAIA,GAAG,KAAK,IAAI,CAACF,mBAAmB,EAAE;QAC3C,IAAI,CAACA,mBAAmB,GAAG,IAAI;MACjC;MAEA,IAAI,CAACmE,GAAG,CACN,SAAS,EACT,GAAGpC,OAAO,CAAC4B,MAAM,IAAI,EAAE,IAAIqB,KAAK,IAAIQ,KAAK,IACtCtF,GAAG,IAAI,GAAG,GAAGA,GAAG,GAAG,GAAG,IAAK,EAC9B,EAAE,EACF6B,OAAO,CACR;MAEDvC,KAAK,CAACgI,IAAI,CAAC,IAAI,CAACjI,QAAQ,CAAC,CACtBuH,MAAM,CAAEvD,OAAwB,IAAKA,OAAO,CAACkE,SAAS,CAACzC,KAAK,CAAC,CAAC,CAC9D1B,OAAO,CAAEC,OAAwB,IAChCA,OAAO,CAACmE,QAAQ,CAAClC,KAAK,EAAEzD,OAAO,EAAE7B,GAAG,CAAC,CACtC;MAEH,IAAI,CAACK,oBAAoB,CAACI,OAAO,CAAC2C,OAAO,CAAEtB,QAAQ,IAAKA,QAAQ,CAACqC,GAAG,CAAC,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;EACQ4C,WAAWA,CAAA;IACjB,IAAI,CAAC9C,GAAG,CAAC,WAAW,EAAE,gBAAgB,IAAI,CAACtB,WAAW,EAAE,EAAE,CAAC;IAC3D,IAAI,CAAC0D,eAAe,EAAE;IACtB,IAAI,CAAClE,cAAc,CAACgB,KAAK,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACb,MAAM,EAAE;MAChB,IAAI,CAACmF,eAAe,EAAE;IACxB,CAAC,MAAM;MACL,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;QACnB,IAAI,CAACC,qBAAqB,EAAE;MAC9B;IACF;IAEA,IAAI,CAACtH,oBAAoB,CAACC,IAAI,CAAC8C,OAAO,CAAEtB,QAAQ,IAAKA,QAAQ,EAAE,CAAC;EAClE;EACA;EACQ2F,eAAeA,CAAA;IACrB,IAAI,CAAC7H,cAAc,IAAIsD,aAAa,CAAC,IAAI,CAACtD,cAAc,CAAC;IACzD,IAAI,CAACA,cAAc,GAAGgI,WAAW,CAC/B,MAAM,IAAI,CAAC1B,aAAa,EAAE,EAC1B,IAAI,CAACvG,mBAAmB,CACzB;EACH;EAEA;EACQgI,qBAAqBA,CAAA;IAC3B,IAAI,IAAI,CAACjF,SAAS,EAAE;MAClB,IAAI,CAACuB,GAAG,CAAC,QAAQ,EAAE,4BAA4B,IAAI,CAACvB,SAAS,EAAE,CAAC;IAClE,CAAC,MAAM;MACL,IAAI,CAACuB,GAAG,CAAC,QAAQ,EAAE,yBAAyB,CAAC;IAC/C;IACA,MAAM4D,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACpF,SAAU,CAAC;IACxD,IAAI,CAACgF,SAAS,GAAG,IAAIlF,MAAM,CAACqF,SAAS,CAAC;IACtC,IAAI,CAACH,SAAS,CAACV,OAAO,GAAIxG,KAAK,IAAI;MACjC,IAAI,CAACyD,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAGzD,KAAoB,CAACC,OAAO,CAAC;MACjE,IAAI,CAACiH,SAAU,CAACK,SAAS,EAAE;IAC7B,CAAC;IACD,IAAI,CAACL,SAAS,CAACR,SAAS,GAAI5B,KAAK,IAAI;MACnC,IAAIA,KAAK,CAAClB,IAAI,CAACkB,KAAK,KAAK,WAAW,EAAE;QACpC,IAAI,CAACY,aAAa,EAAE;MACtB;IACF,CAAC;IACD,IAAI,CAACwB,SAAS,CAACM,WAAW,CAAC;MACzB1C,KAAK,EAAE,OAAO;MACd2C,QAAQ,EAAE,IAAI,CAACtI;KAChB,CAAC;EACJ;EACA;EACQyH,YAAYA,CAAC9B,KAAU;IAC7B,IAAI,CAACrB,GAAG,CAAC,WAAW,EAAE,OAAO,EAAEqB,KAAK,CAAC;IACrC,IAAI,CAAC4C,iBAAiB,EAAE;IACxB,IAAI,CAACtI,cAAc,IAAIsD,aAAa,CAAC,IAAI,CAACtD,cAAc,CAAC;IACzD,IAAI,CAACuC,cAAc,CAACgG,eAAe,EAAE;IACrC,IAAI,CAAC9H,oBAAoB,CAACE,KAAK,CAAC6C,OAAO,CAAEtB,QAAQ,IAAKA,QAAQ,CAACwD,KAAK,CAAC,CAAC;EACxE;EAEA;EACQ2B,YAAYA,CAACzG,KAAY;IAC/B,IAAI,CAACyD,GAAG,CAAC,WAAW,EAAE,GAAGzD,KAAK,EAAE,CAAC;IACjC,IAAI,CAAC0H,iBAAiB,EAAE;IACxB,IAAI,CAAC7H,oBAAoB,CAACG,KAAK,CAAC4C,OAAO,CAAEtB,QAAQ,IAAKA,QAAQ,CAACtB,KAAK,CAAC,CAAC;EACxE;EAEA;EACQ0H,iBAAiBA,CAAA;IACvB,IAAI,CAAC7I,QAAQ,CAAC+D,OAAO,CAAEC,OAAwB,IAC7CA,OAAO,CAACmE,QAAQ,CAACvJ,cAAc,CAACuC,KAAK,CAAC,CACvC;EACH;EAEA;EACQqC,aAAaA,CACnBuF,GAAW,EACX3I,MAAiC;IAEjC,IAAI6B,MAAM,CAAC+G,IAAI,CAAC5I,MAAM,CAAC,CAACkE,MAAM,KAAK,CAAC,EAAE;MACpC,OAAOyE,GAAG;IACZ;IACA,MAAME,MAAM,GAAGF,GAAG,CAACG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;IAC1C,MAAMC,KAAK,GAAG,IAAIC,eAAe,CAAChJ,MAAM,CAAC;IACzC,OAAO,GAAG2I,GAAG,GAAGE,MAAM,GAAGE,KAAK,EAAE;EAClC;EAEQV,gBAAgBA,CAACM,GAAuB;IAC9C,IAAIM,UAAkB;IACtB,IAAIN,GAAG,EAAE;MACPM,UAAU,GAAGN,GAAG;IAClB,CAAC,MAAM;MACL,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC9J,aAAa,CAAC,EAAE;QAAE+J,IAAI,EAAE;MAAwB,CAAE,CAAC;MAC1EH,UAAU,GAAGI,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACxC;IACA,OAAOD,UAAU;EACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}