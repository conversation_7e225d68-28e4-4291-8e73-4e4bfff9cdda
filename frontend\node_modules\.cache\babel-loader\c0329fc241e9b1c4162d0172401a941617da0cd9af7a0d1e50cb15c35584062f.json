{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\pages\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    signIn\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const {\n        error\n      } = await signIn(email, password);\n      if (error) {\n        setError(error.message);\n      } else {\n        navigate('/');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-md mx-auto bg-white rounded-lg shadow-lg p-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-center mb-6\",\n      children: \"Sign In\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          id: \"email\",\n          value: email,\n          onChange: e => setEmail(e.target.value),\n          required: true,\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n          placeholder: \"<EMAIL>\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          id: \"password\",\n          value: password,\n          onChange: e => setPassword(e.target.value),\n          required: true,\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n          placeholder: \"Your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n        children: loading ? 'Signing In...' : 'Sign In'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          className: \"text-primary-600 hover:text-primary-700\",\n          children: \"Sign up here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"mx8zjDjIFSqY9FaWDro1tv/r678=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "loading", "setLoading", "error", "setError", "signIn", "navigate", "handleSubmit", "e", "preventDefault", "message", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "required", "placeholder", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Login: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  \n  const { signIn } = useAuth();\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const { error } = await signIn(email, password);\n      \n      if (error) {\n        setError(error.message);\n      } else {\n        navigate('/');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-lg p-8\">\n      <h2 className=\"text-2xl font-bold text-center mb-6\">Sign In</h2>\n      \n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {error}\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email\n          </label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            value={email}\n            onChange={(e) => setEmail(e.target.value)}\n            required\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n            placeholder=\"<EMAIL>\"\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Password\n          </label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            value={password}\n            onChange={(e) => setPassword(e.target.value)}\n            required\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n            placeholder=\"Your password\"\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {loading ? 'Signing In...' : 'Sign In'}\n        </button>\n      </form>\n\n      <div className=\"mt-6 text-center\">\n        <p className=\"text-gray-600\">\n          Don't have an account?{' '}\n          <Link to=\"/register\" className=\"text-primary-600 hover:text-primary-700\">\n            Sign up here\n          </Link>\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEgB;EAAO,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC5B,MAAMc,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM;QAAED;MAAM,CAAC,GAAG,MAAME,MAAM,CAACR,KAAK,EAAEE,QAAQ,CAAC;MAE/C,IAAII,KAAK,EAAE;QACTC,QAAQ,CAACD,KAAK,CAACO,OAAO,CAAC;MACzB,CAAC,MAAM;QACLJ,QAAQ,CAAC,GAAG,CAAC;MACf;IACF,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZP,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA;IAAKkB,SAAS,EAAC,oDAAoD;IAAAC,QAAA,gBACjEnB,OAAA;MAAIkB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE/Dd,KAAK,iBACJT,OAAA;MAAKkB,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClFV;IAAK;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDvB,OAAA;MAAMwB,QAAQ,EAAEX,YAAa;MAACK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACjDnB,OAAA;QAAAmB,QAAA,gBACEnB,OAAA;UAAOyB,OAAO,EAAC,OAAO;UAACP,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRvB,OAAA;UACE0B,IAAI,EAAC,OAAO;UACZC,EAAE,EAAC,OAAO;UACVC,KAAK,EAAEzB,KAAM;UACb0B,QAAQ,EAAGf,CAAC,IAAKV,QAAQ,CAACU,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;UAC1CG,QAAQ;UACRb,SAAS,EAAC,2GAA2G;UACrHc,WAAW,EAAC;QAAgB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvB,OAAA;QAAAmB,QAAA,gBACEnB,OAAA;UAAOyB,OAAO,EAAC,UAAU;UAACP,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRvB,OAAA;UACE0B,IAAI,EAAC,UAAU;UACfC,EAAE,EAAC,UAAU;UACbC,KAAK,EAAEvB,QAAS;UAChBwB,QAAQ,EAAGf,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;UAC7CG,QAAQ;UACRb,SAAS,EAAC,2GAA2G;UACrHc,WAAW,EAAC;QAAe;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvB,OAAA;QACE0B,IAAI,EAAC,QAAQ;QACbO,QAAQ,EAAE1B,OAAQ;QAClBW,SAAS,EAAC,mLAAmL;QAAAC,QAAA,EAE5LZ,OAAO,GAAG,eAAe,GAAG;MAAS;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPvB,OAAA;MAAKkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BnB,OAAA;QAAGkB,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,wBACL,EAAC,GAAG,eAC1BnB,OAAA,CAACJ,IAAI;UAACsC,EAAE,EAAC,WAAW;UAAChB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAzFID,KAAe;EAAA,QAMAH,OAAO,EACTD,WAAW;AAAA;AAAAsC,EAAA,GAPxBlC,KAAe;AA2FrB,eAAeA,KAAK;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}