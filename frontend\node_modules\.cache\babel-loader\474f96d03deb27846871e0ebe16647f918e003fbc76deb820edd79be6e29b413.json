{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\pages\\\\SearchLost.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchLost = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-900 mb-6\",\n      children: \"Search Lost Discs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 text-center py-8\",\n        children: \"Search Lost Discs functionality will be implemented here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = SearchLost;\nexport default SearchLost;\nvar _c;\n$RefreshReg$(_c, \"SearchLost\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SearchLost", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/pages/SearchLost.tsx"], "sourcesContent": ["import React from 'react';\n\nconst SearchLost: React.FC = () => {\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Search Lost Discs</h1>\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <p className=\"text-gray-600 text-center py-8\">\n          Search Lost Discs functionality will be implemented here.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default SearchLost;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,oBACED,OAAA;IAAKE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCH,OAAA;MAAIE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5EP,OAAA;MAAKE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDH,OAAA;QAAGE,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAE9C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAXIP,UAAoB;AAa1B,eAAeA,UAAU;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}