{"ast": null, "code": "/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('');\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('');\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n  const charMap = new Array(128);\n  for (let i = 0; i < charMap.length; i += 1) {\n    charMap[i] = -1;\n  }\n  for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n    charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n  }\n  for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n    charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n  }\n  return charMap;\n})();\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nexport function byteToBase64URL(byte, state, emit) {\n  if (byte !== null) {\n    state.queue = state.queue << 8 | byte;\n    state.queuedBits += 8;\n    while (state.queuedBits >= 6) {\n      const pos = state.queue >> state.queuedBits - 6 & 63;\n      emit(TO_BASE64URL[pos]);\n      state.queuedBits -= 6;\n    }\n  } else if (state.queuedBits > 0) {\n    state.queue = state.queue << 6 - state.queuedBits;\n    state.queuedBits = 6;\n    while (state.queuedBits >= 6) {\n      const pos = state.queue >> state.queuedBits - 6 & 63;\n      emit(TO_BASE64URL[pos]);\n      state.queuedBits -= 6;\n    }\n  }\n}\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nexport function byteFromBase64URL(charCode, state, emit) {\n  const bits = FROM_BASE64URL[charCode];\n  if (bits > -1) {\n    // valid Base64-URL character\n    state.queue = state.queue << 6 | bits;\n    state.queuedBits += 6;\n    while (state.queuedBits >= 8) {\n      emit(state.queue >> state.queuedBits - 8 & 0xff);\n      state.queuedBits -= 8;\n    }\n  } else if (bits === -2) {\n    // ignore spaces, tabs, newlines, =\n    return;\n  } else {\n    throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`);\n  }\n}\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nexport function stringToBase64URL(str) {\n  const base64 = [];\n  const emitter = char => {\n    base64.push(char);\n  };\n  const state = {\n    queue: 0,\n    queuedBits: 0\n  };\n  stringToUTF8(str, byte => {\n    byteToBase64URL(byte, state, emitter);\n  });\n  byteToBase64URL(null, state, emitter);\n  return base64.join('');\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nexport function stringFromBase64URL(str) {\n  const conv = [];\n  const utf8Emit = codepoint => {\n    conv.push(String.fromCodePoint(codepoint));\n  };\n  const utf8State = {\n    utf8seq: 0,\n    codepoint: 0\n  };\n  const b64State = {\n    queue: 0,\n    queuedBits: 0\n  };\n  const byteEmit = byte => {\n    stringFromUTF8(byte, utf8State, utf8Emit);\n  };\n  for (let i = 0; i < str.length; i += 1) {\n    byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit);\n  }\n  return conv.join('');\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nexport function codepointToUTF8(codepoint, emit) {\n  if (codepoint <= 0x7f) {\n    emit(codepoint);\n    return;\n  } else if (codepoint <= 0x7ff) {\n    emit(0xc0 | codepoint >> 6);\n    emit(0x80 | codepoint & 0x3f);\n    return;\n  } else if (codepoint <= 0xffff) {\n    emit(0xe0 | codepoint >> 12);\n    emit(0x80 | codepoint >> 6 & 0x3f);\n    emit(0x80 | codepoint & 0x3f);\n    return;\n  } else if (codepoint <= 0x10ffff) {\n    emit(0xf0 | codepoint >> 18);\n    emit(0x80 | codepoint >> 12 & 0x3f);\n    emit(0x80 | codepoint >> 6 & 0x3f);\n    emit(0x80 | codepoint & 0x3f);\n    return;\n  }\n  throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nexport function stringToUTF8(str, emit) {\n  for (let i = 0; i < str.length; i += 1) {\n    let codepoint = str.charCodeAt(i);\n    if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n      // most UTF-16 codepoints are Unicode codepoints, except values in this\n      // range where the next UTF-16 codepoint needs to be combined with the\n      // current one to get the Unicode codepoint\n      const highSurrogate = (codepoint - 0xd800) * 0x400 & 0xffff;\n      const lowSurrogate = str.charCodeAt(i + 1) - 0xdc00 & 0xffff;\n      codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n      i += 1;\n    }\n    codepointToUTF8(codepoint, emit);\n  }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nexport function stringFromUTF8(byte, state, emit) {\n  if (state.utf8seq === 0) {\n    if (byte <= 0x7f) {\n      emit(byte);\n      return;\n    }\n    // count the number of 1 leading bits until you reach 0\n    for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n      if ((byte >> 7 - leadingBit & 1) === 0) {\n        state.utf8seq = leadingBit;\n        break;\n      }\n    }\n    if (state.utf8seq === 2) {\n      state.codepoint = byte & 31;\n    } else if (state.utf8seq === 3) {\n      state.codepoint = byte & 15;\n    } else if (state.utf8seq === 4) {\n      state.codepoint = byte & 7;\n    } else {\n      throw new Error('Invalid UTF-8 sequence');\n    }\n    state.utf8seq -= 1;\n  } else if (state.utf8seq > 0) {\n    if (byte <= 0x7f) {\n      throw new Error('Invalid UTF-8 sequence');\n    }\n    state.codepoint = state.codepoint << 6 | byte & 63;\n    state.utf8seq -= 1;\n    if (state.utf8seq === 0) {\n      emit(state.codepoint);\n    }\n  }\n}\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\nexport function base64UrlToUint8Array(str) {\n  const result = [];\n  const state = {\n    queue: 0,\n    queuedBits: 0\n  };\n  const onByte = byte => {\n    result.push(byte);\n  };\n  for (let i = 0; i < str.length; i += 1) {\n    byteFromBase64URL(str.charCodeAt(i), state, onByte);\n  }\n  return new Uint8Array(result);\n}\nexport function stringToUint8Array(str) {\n  const result = [];\n  stringToUTF8(str, byte => result.push(byte));\n  return new Uint8Array(result);\n}\nexport function bytesToBase64URL(bytes) {\n  const result = [];\n  const state = {\n    queue: 0,\n    queuedBits: 0\n  };\n  const onChar = char => {\n    result.push(char);\n  };\n  bytes.forEach(byte => byteToBase64URL(byte, state, onChar));\n  // always call with `null` after processing all bytes\n  byteToBase64URL(null, state, onChar);\n  return result.join('');\n}", "map": {"version": 3, "names": ["TO_BASE64URL", "split", "IGNORE_BASE64URL", "FROM_BASE64URL", "charMap", "Array", "i", "length", "charCodeAt", "byteToBase64URL", "byte", "state", "emit", "queue", "queuedBits", "pos", "byteFromBase64URL", "charCode", "bits", "Error", "String", "fromCharCode", "stringToBase64URL", "str", "base64", "emitter", "char", "push", "stringToUTF8", "join", "stringFromBase64URL", "conv", "utf8Emit", "codepoint", "fromCodePoint", "utf8State", "utf8seq", "b64State", "byteEmit", "stringFromUTF8", "codepointToUTF8", "toString", "highSurrogate", "lowSurrogate", "leadingBit", "base64UrlToUint8Array", "result", "onByte", "Uint8Array", "stringToUint8Array", "bytesToBase64URL", "bytes", "onChar", "for<PERSON>ach"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\auth-js\\src\\lib\\base64url.ts"], "sourcesContent": ["/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('')\n\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('')\n\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n  const charMap: number[] = new Array(128)\n\n  for (let i = 0; i < charMap.length; i += 1) {\n    charMap[i] = -1\n  }\n\n  for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n    charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2\n  }\n\n  for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n    charMap[TO_BASE64URL[i].charCodeAt(0)] = i\n  }\n\n  return charMap\n})()\n\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nexport function byteToBase64URL(\n  byte: number | null,\n  state: { queue: number; queuedBits: number },\n  emit: (char: string) => void\n) {\n  if (byte !== null) {\n    state.queue = (state.queue << 8) | byte\n    state.queuedBits += 8\n\n    while (state.queuedBits >= 6) {\n      const pos = (state.queue >> (state.queuedBits - 6)) & 63\n      emit(TO_BASE64URL[pos])\n      state.queuedBits -= 6\n    }\n  } else if (state.queuedBits > 0) {\n    state.queue = state.queue << (6 - state.queuedBits)\n    state.queuedBits = 6\n\n    while (state.queuedBits >= 6) {\n      const pos = (state.queue >> (state.queuedBits - 6)) & 63\n      emit(TO_BASE64URL[pos])\n      state.queuedBits -= 6\n    }\n  }\n}\n\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nexport function byteFromBase64URL(\n  charCode: number,\n  state: { queue: number; queuedBits: number },\n  emit: (byte: number) => void\n) {\n  const bits = FROM_BASE64URL[charCode]\n\n  if (bits > -1) {\n    // valid Base64-URL character\n    state.queue = (state.queue << 6) | bits\n    state.queuedBits += 6\n\n    while (state.queuedBits >= 8) {\n      emit((state.queue >> (state.queuedBits - 8)) & 0xff)\n      state.queuedBits -= 8\n    }\n  } else if (bits === -2) {\n    // ignore spaces, tabs, newlines, =\n    return\n  } else {\n    throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`)\n  }\n}\n\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nexport function stringToBase64URL(str: string) {\n  const base64: string[] = []\n\n  const emitter = (char: string) => {\n    base64.push(char)\n  }\n\n  const state = { queue: 0, queuedBits: 0 }\n\n  stringToUTF8(str, (byte: number) => {\n    byteToBase64URL(byte, state, emitter)\n  })\n\n  byteToBase64URL(null, state, emitter)\n\n  return base64.join('')\n}\n\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nexport function stringFromBase64URL(str: string) {\n  const conv: string[] = []\n\n  const utf8Emit = (codepoint: number) => {\n    conv.push(String.fromCodePoint(codepoint))\n  }\n\n  const utf8State = {\n    utf8seq: 0,\n    codepoint: 0,\n  }\n\n  const b64State = { queue: 0, queuedBits: 0 }\n\n  const byteEmit = (byte: number) => {\n    stringFromUTF8(byte, utf8State, utf8Emit)\n  }\n\n  for (let i = 0; i < str.length; i += 1) {\n    byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit)\n  }\n\n  return conv.join('')\n}\n\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nexport function codepointToUTF8(codepoint: number, emit: (byte: number) => void) {\n  if (codepoint <= 0x7f) {\n    emit(codepoint)\n    return\n  } else if (codepoint <= 0x7ff) {\n    emit(0xc0 | (codepoint >> 6))\n    emit(0x80 | (codepoint & 0x3f))\n    return\n  } else if (codepoint <= 0xffff) {\n    emit(0xe0 | (codepoint >> 12))\n    emit(0x80 | ((codepoint >> 6) & 0x3f))\n    emit(0x80 | (codepoint & 0x3f))\n    return\n  } else if (codepoint <= 0x10ffff) {\n    emit(0xf0 | (codepoint >> 18))\n    emit(0x80 | ((codepoint >> 12) & 0x3f))\n    emit(0x80 | ((codepoint >> 6) & 0x3f))\n    emit(0x80 | (codepoint & 0x3f))\n    return\n  }\n\n  throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`)\n}\n\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nexport function stringToUTF8(str: string, emit: (byte: number) => void) {\n  for (let i = 0; i < str.length; i += 1) {\n    let codepoint = str.charCodeAt(i)\n\n    if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n      // most UTF-16 codepoints are Unicode codepoints, except values in this\n      // range where the next UTF-16 codepoint needs to be combined with the\n      // current one to get the Unicode codepoint\n      const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff\n      const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff\n      codepoint = (lowSurrogate | highSurrogate) + 0x10000\n      i += 1\n    }\n\n    codepointToUTF8(codepoint, emit)\n  }\n}\n\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nexport function stringFromUTF8(\n  byte: number,\n  state: { utf8seq: number; codepoint: number },\n  emit: (codepoint: number) => void\n) {\n  if (state.utf8seq === 0) {\n    if (byte <= 0x7f) {\n      emit(byte)\n      return\n    }\n\n    // count the number of 1 leading bits until you reach 0\n    for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n      if (((byte >> (7 - leadingBit)) & 1) === 0) {\n        state.utf8seq = leadingBit\n        break\n      }\n    }\n\n    if (state.utf8seq === 2) {\n      state.codepoint = byte & 31\n    } else if (state.utf8seq === 3) {\n      state.codepoint = byte & 15\n    } else if (state.utf8seq === 4) {\n      state.codepoint = byte & 7\n    } else {\n      throw new Error('Invalid UTF-8 sequence')\n    }\n\n    state.utf8seq -= 1\n  } else if (state.utf8seq > 0) {\n    if (byte <= 0x7f) {\n      throw new Error('Invalid UTF-8 sequence')\n    }\n\n    state.codepoint = (state.codepoint << 6) | (byte & 63)\n    state.utf8seq -= 1\n\n    if (state.utf8seq === 0) {\n      emit(state.codepoint)\n    }\n  }\n}\n\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\n\nexport function base64UrlToUint8Array(str: string): Uint8Array {\n  const result: number[] = []\n  const state = { queue: 0, queuedBits: 0 }\n\n  const onByte = (byte: number) => {\n    result.push(byte)\n  }\n\n  for (let i = 0; i < str.length; i += 1) {\n    byteFromBase64URL(str.charCodeAt(i), state, onByte)\n  }\n\n  return new Uint8Array(result)\n}\n\nexport function stringToUint8Array(str: string): Uint8Array {\n  const result: number[] = []\n  stringToUTF8(str, (byte: number) => result.push(byte))\n  return new Uint8Array(result)\n}\n\nexport function bytesToBase64URL(bytes: Uint8Array) {\n  const result: string[] = []\n  const state = { queue: 0, queuedBits: 0 }\n\n  const onChar = (char: string) => {\n    result.push(char)\n  }\n\n  bytes.forEach((byte) => byteToBase64URL(byte, state, onChar))\n\n  // always call with `null` after processing all bytes\n  byteToBase64URL(null, state, onChar)\n\n  return result.join('')\n}\n"], "mappings": "AAAA;;;;;AAMA;;;;AAIA,MAAMA,YAAY,GAAG,kEAAkE,CAACC,KAAK,CAAC,EAAE,CAAC;AAEjG;;;;AAIA,MAAMC,gBAAgB,GAAG,UAAU,CAACD,KAAK,CAAC,EAAE,CAAC;AAE7C;;;;AAIA,MAAME,cAAc,GAAG,CAAC,MAAK;EAC3B,MAAMC,OAAO,GAAa,IAAIC,KAAK,CAAC,GAAG,CAAC;EAExC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC1CF,OAAO,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;;EAGjB,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,gBAAgB,CAACK,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACnDF,OAAO,CAACF,gBAAgB,CAACI,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;EAGjD,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,YAAY,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC/CF,OAAO,CAACJ,YAAY,CAACM,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGF,CAAC;;EAG5C,OAAOF,OAAO;AAChB,CAAC,EAAC,CAAE;AAEJ;;;;;;;AAOA,OAAM,SAAUK,eAAeA,CAC7BC,IAAmB,EACnBC,KAA4C,EAC5CC,IAA4B;EAE5B,IAAIF,IAAI,KAAK,IAAI,EAAE;IACjBC,KAAK,CAACE,KAAK,GAAIF,KAAK,CAACE,KAAK,IAAI,CAAC,GAAIH,IAAI;IACvCC,KAAK,CAACG,UAAU,IAAI,CAAC;IAErB,OAAOH,KAAK,CAACG,UAAU,IAAI,CAAC,EAAE;MAC5B,MAAMC,GAAG,GAAIJ,KAAK,CAACE,KAAK,IAAKF,KAAK,CAACG,UAAU,GAAG,CAAE,GAAI,EAAE;MACxDF,IAAI,CAACZ,YAAY,CAACe,GAAG,CAAC,CAAC;MACvBJ,KAAK,CAACG,UAAU,IAAI,CAAC;;GAExB,MAAM,IAAIH,KAAK,CAACG,UAAU,GAAG,CAAC,EAAE;IAC/BH,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAK,CAAC,GAAGF,KAAK,CAACG,UAAW;IACnDH,KAAK,CAACG,UAAU,GAAG,CAAC;IAEpB,OAAOH,KAAK,CAACG,UAAU,IAAI,CAAC,EAAE;MAC5B,MAAMC,GAAG,GAAIJ,KAAK,CAACE,KAAK,IAAKF,KAAK,CAACG,UAAU,GAAG,CAAE,GAAI,EAAE;MACxDF,IAAI,CAACZ,YAAY,CAACe,GAAG,CAAC,CAAC;MACvBJ,KAAK,CAACG,UAAU,IAAI,CAAC;;;AAG3B;AAEA;;;;;;;AAOA,OAAM,SAAUE,iBAAiBA,CAC/BC,QAAgB,EAChBN,KAA4C,EAC5CC,IAA4B;EAE5B,MAAMM,IAAI,GAAGf,cAAc,CAACc,QAAQ,CAAC;EAErC,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAE;IACb;IACAP,KAAK,CAACE,KAAK,GAAIF,KAAK,CAACE,KAAK,IAAI,CAAC,GAAIK,IAAI;IACvCP,KAAK,CAACG,UAAU,IAAI,CAAC;IAErB,OAAOH,KAAK,CAACG,UAAU,IAAI,CAAC,EAAE;MAC5BF,IAAI,CAAED,KAAK,CAACE,KAAK,IAAKF,KAAK,CAACG,UAAU,GAAG,CAAE,GAAI,IAAI,CAAC;MACpDH,KAAK,CAACG,UAAU,IAAI,CAAC;;GAExB,MAAM,IAAII,IAAI,KAAK,CAAC,CAAC,EAAE;IACtB;IACA;GACD,MAAM;IACL,MAAM,IAAIC,KAAK,CAAC,iCAAiCC,MAAM,CAACC,YAAY,CAACJ,QAAQ,CAAC,GAAG,CAAC;;AAEtF;AAEA;;;;;;;AAOA,OAAM,SAAUK,iBAAiBA,CAACC,GAAW;EAC3C,MAAMC,MAAM,GAAa,EAAE;EAE3B,MAAMC,OAAO,GAAIC,IAAY,IAAI;IAC/BF,MAAM,CAACG,IAAI,CAACD,IAAI,CAAC;EACnB,CAAC;EAED,MAAMf,KAAK,GAAG;IAAEE,KAAK,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAC,CAAE;EAEzCc,YAAY,CAACL,GAAG,EAAGb,IAAY,IAAI;IACjCD,eAAe,CAACC,IAAI,EAAEC,KAAK,EAAEc,OAAO,CAAC;EACvC,CAAC,CAAC;EAEFhB,eAAe,CAAC,IAAI,EAAEE,KAAK,EAAEc,OAAO,CAAC;EAErC,OAAOD,MAAM,CAACK,IAAI,CAAC,EAAE,CAAC;AACxB;AAEA;;;;;;AAMA,OAAM,SAAUC,mBAAmBA,CAACP,GAAW;EAC7C,MAAMQ,IAAI,GAAa,EAAE;EAEzB,MAAMC,QAAQ,GAAIC,SAAiB,IAAI;IACrCF,IAAI,CAACJ,IAAI,CAACP,MAAM,CAACc,aAAa,CAACD,SAAS,CAAC,CAAC;EAC5C,CAAC;EAED,MAAME,SAAS,GAAG;IAChBC,OAAO,EAAE,CAAC;IACVH,SAAS,EAAE;GACZ;EAED,MAAMI,QAAQ,GAAG;IAAExB,KAAK,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAC,CAAE;EAE5C,MAAMwB,QAAQ,GAAI5B,IAAY,IAAI;IAChC6B,cAAc,CAAC7B,IAAI,EAAEyB,SAAS,EAAEH,QAAQ,CAAC;EAC3C,CAAC;EAED,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,GAAG,CAAChB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACtCU,iBAAiB,CAACO,GAAG,CAACf,UAAU,CAACF,CAAC,CAAC,EAAE+B,QAAQ,EAAEC,QAAQ,CAAC;;EAG1D,OAAOP,IAAI,CAACF,IAAI,CAAC,EAAE,CAAC;AACtB;AAEA;;;;;;AAMA,OAAM,SAAUW,eAAeA,CAACP,SAAiB,EAAErB,IAA4B;EAC7E,IAAIqB,SAAS,IAAI,IAAI,EAAE;IACrBrB,IAAI,CAACqB,SAAS,CAAC;IACf;GACD,MAAM,IAAIA,SAAS,IAAI,KAAK,EAAE;IAC7BrB,IAAI,CAAC,IAAI,GAAIqB,SAAS,IAAI,CAAE,CAAC;IAC7BrB,IAAI,CAAC,IAAI,GAAIqB,SAAS,GAAG,IAAK,CAAC;IAC/B;GACD,MAAM,IAAIA,SAAS,IAAI,MAAM,EAAE;IAC9BrB,IAAI,CAAC,IAAI,GAAIqB,SAAS,IAAI,EAAG,CAAC;IAC9BrB,IAAI,CAAC,IAAI,GAAKqB,SAAS,IAAI,CAAC,GAAI,IAAK,CAAC;IACtCrB,IAAI,CAAC,IAAI,GAAIqB,SAAS,GAAG,IAAK,CAAC;IAC/B;GACD,MAAM,IAAIA,SAAS,IAAI,QAAQ,EAAE;IAChCrB,IAAI,CAAC,IAAI,GAAIqB,SAAS,IAAI,EAAG,CAAC;IAC9BrB,IAAI,CAAC,IAAI,GAAKqB,SAAS,IAAI,EAAE,GAAI,IAAK,CAAC;IACvCrB,IAAI,CAAC,IAAI,GAAKqB,SAAS,IAAI,CAAC,GAAI,IAAK,CAAC;IACtCrB,IAAI,CAAC,IAAI,GAAIqB,SAAS,GAAG,IAAK,CAAC;IAC/B;;EAGF,MAAM,IAAId,KAAK,CAAC,mCAAmCc,SAAS,CAACQ,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AAC9E;AAEA;;;;;;AAMA,OAAM,SAAUb,YAAYA,CAACL,GAAW,EAAEX,IAA4B;EACpE,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,GAAG,CAAChB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACtC,IAAI2B,SAAS,GAAGV,GAAG,CAACf,UAAU,CAACF,CAAC,CAAC;IAEjC,IAAI2B,SAAS,GAAG,MAAM,IAAIA,SAAS,IAAI,MAAM,EAAE;MAC7C;MACA;MACA;MACA,MAAMS,aAAa,GAAI,CAACT,SAAS,GAAG,MAAM,IAAI,KAAK,GAAI,MAAM;MAC7D,MAAMU,YAAY,GAAIpB,GAAG,CAACf,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAI,MAAM;MAC9D2B,SAAS,GAAG,CAACU,YAAY,GAAGD,aAAa,IAAI,OAAO;MACpDpC,CAAC,IAAI,CAAC;;IAGRkC,eAAe,CAACP,SAAS,EAAErB,IAAI,CAAC;;AAEpC;AAEA;;;;;;;;AAQA,OAAM,SAAU2B,cAAcA,CAC5B7B,IAAY,EACZC,KAA6C,EAC7CC,IAAiC;EAEjC,IAAID,KAAK,CAACyB,OAAO,KAAK,CAAC,EAAE;IACvB,IAAI1B,IAAI,IAAI,IAAI,EAAE;MAChBE,IAAI,CAACF,IAAI,CAAC;MACV;;IAGF;IACA,KAAK,IAAIkC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG,CAAC,EAAEA,UAAU,IAAI,CAAC,EAAE;MACxD,IAAI,CAAElC,IAAI,IAAK,CAAC,GAAGkC,UAAW,GAAI,CAAC,MAAM,CAAC,EAAE;QAC1CjC,KAAK,CAACyB,OAAO,GAAGQ,UAAU;QAC1B;;;IAIJ,IAAIjC,KAAK,CAACyB,OAAO,KAAK,CAAC,EAAE;MACvBzB,KAAK,CAACsB,SAAS,GAAGvB,IAAI,GAAG,EAAE;KAC5B,MAAM,IAAIC,KAAK,CAACyB,OAAO,KAAK,CAAC,EAAE;MAC9BzB,KAAK,CAACsB,SAAS,GAAGvB,IAAI,GAAG,EAAE;KAC5B,MAAM,IAAIC,KAAK,CAACyB,OAAO,KAAK,CAAC,EAAE;MAC9BzB,KAAK,CAACsB,SAAS,GAAGvB,IAAI,GAAG,CAAC;KAC3B,MAAM;MACL,MAAM,IAAIS,KAAK,CAAC,wBAAwB,CAAC;;IAG3CR,KAAK,CAACyB,OAAO,IAAI,CAAC;GACnB,MAAM,IAAIzB,KAAK,CAACyB,OAAO,GAAG,CAAC,EAAE;IAC5B,IAAI1B,IAAI,IAAI,IAAI,EAAE;MAChB,MAAM,IAAIS,KAAK,CAAC,wBAAwB,CAAC;;IAG3CR,KAAK,CAACsB,SAAS,GAAItB,KAAK,CAACsB,SAAS,IAAI,CAAC,GAAKvB,IAAI,GAAG,EAAG;IACtDC,KAAK,CAACyB,OAAO,IAAI,CAAC;IAElB,IAAIzB,KAAK,CAACyB,OAAO,KAAK,CAAC,EAAE;MACvBxB,IAAI,CAACD,KAAK,CAACsB,SAAS,CAAC;;;AAG3B;AAEA;;;AAIA,OAAM,SAAUY,qBAAqBA,CAACtB,GAAW;EAC/C,MAAMuB,MAAM,GAAa,EAAE;EAC3B,MAAMnC,KAAK,GAAG;IAAEE,KAAK,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAC,CAAE;EAEzC,MAAMiC,MAAM,GAAIrC,IAAY,IAAI;IAC9BoC,MAAM,CAACnB,IAAI,CAACjB,IAAI,CAAC;EACnB,CAAC;EAED,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,GAAG,CAAChB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACtCU,iBAAiB,CAACO,GAAG,CAACf,UAAU,CAACF,CAAC,CAAC,EAAEK,KAAK,EAAEoC,MAAM,CAAC;;EAGrD,OAAO,IAAIC,UAAU,CAACF,MAAM,CAAC;AAC/B;AAEA,OAAM,SAAUG,kBAAkBA,CAAC1B,GAAW;EAC5C,MAAMuB,MAAM,GAAa,EAAE;EAC3BlB,YAAY,CAACL,GAAG,EAAGb,IAAY,IAAKoC,MAAM,CAACnB,IAAI,CAACjB,IAAI,CAAC,CAAC;EACtD,OAAO,IAAIsC,UAAU,CAACF,MAAM,CAAC;AAC/B;AAEA,OAAM,SAAUI,gBAAgBA,CAACC,KAAiB;EAChD,MAAML,MAAM,GAAa,EAAE;EAC3B,MAAMnC,KAAK,GAAG;IAAEE,KAAK,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAC,CAAE;EAEzC,MAAMsC,MAAM,GAAI1B,IAAY,IAAI;IAC9BoB,MAAM,CAACnB,IAAI,CAACD,IAAI,CAAC;EACnB,CAAC;EAEDyB,KAAK,CAACE,OAAO,CAAE3C,IAAI,IAAKD,eAAe,CAACC,IAAI,EAAEC,KAAK,EAAEyC,MAAM,CAAC,CAAC;EAE7D;EACA3C,eAAe,CAAC,IAAI,EAAEE,KAAK,EAAEyC,MAAM,CAAC;EAEpC,OAAON,MAAM,CAACjB,IAAI,CAAC,EAAE,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}