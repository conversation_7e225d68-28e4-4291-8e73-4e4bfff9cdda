{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\frontend\\\\src\\\\pages\\\\MyReports.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Navigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyReports = () => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 12\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-900 mb-6\",\n      children: \"My Reports\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 text-center py-8\",\n        children: \"My Reports dashboard will be implemented here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(MyReports, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c = MyReports;\nexport default MyReports;\nvar _c;\n$RefreshReg$(_c, \"MyReports\");", "map": {"version": 3, "names": ["React", "useAuth", "Navigate", "jsxDEV", "_jsxDEV", "MyReports", "_s", "user", "loading", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/frontend/src/pages/MyReports.tsx"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Navigate } from 'react-router-dom';\n\nconst MyReports: React.FC = () => {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return <div className=\"text-center py-8\">Loading...</div>;\n  }\n\n  if (!user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">My Reports</h1>\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <p className=\"text-gray-600 text-center py-8\">\n          My Reports dashboard will be implemented here.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default MyReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGP,OAAO,CAAC,CAAC;EAEnC,IAAIO,OAAO,EAAE;IACX,oBAAOJ,OAAA;MAAKK,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC3D;EAEA,IAAI,CAACP,IAAI,EAAE;IACT,oBAAOH,OAAA,CAACF,QAAQ;MAACa,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,oBACEV,OAAA;IAAKK,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCN,OAAA;MAAIK,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrEV,OAAA;MAAKK,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDN,OAAA;QAAGK,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAE9C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACR,EAAA,CArBID,SAAmB;EAAA,QACGJ,OAAO;AAAA;AAAAgB,EAAA,GAD7BZ,SAAmB;AAuBzB,eAAeA,SAAS;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}